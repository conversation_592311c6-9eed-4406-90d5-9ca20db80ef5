import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { MongooseModule } from '@nestjs/mongoose';
import { GameController } from './game.controller';
import { GameService } from './game.service';
import { GameToTelegramHandle } from './handler/game-to-telegram.handle';
import { SyncConfig, SyncConfigSchema } from '../../schemas/sync-config.schema';
import { MessageStatus, MessageStatusSchema } from '../../schemas/message-status.schema';
import { queues } from '../../constant/mq.constant';

@Module({
  imports: [
    HttpModule,
    BullModule.registerQueue({
      name: queues.GAME_CHAT_SYNC_TG_QUEUE,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      },
    }),
    MongooseModule.forFeature([
      { name: SyncConfig.name, schema: SyncConfigSchema },
      { name: MessageStatus.name, schema: MessageStatusSchema },
    ]),
  ],
  controllers: [GameController],
  providers: [GameService, GameToTelegramHandle],
  exports: [GameService],
})
export class GameModule {}
