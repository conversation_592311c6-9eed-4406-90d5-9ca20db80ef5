import { Controller, Post, Body, Get, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { GameService } from './game.service';
import { SyncDto } from '../../dto/sync.dto';
import { BasicUtils } from '../../utils/basic.utils';

@ApiTags('游戏聊天')
@Controller('game')
export class GameController {
  private readonly logger = new Logger(GameController.name);

  constructor(private readonly gameService: GameService) {}

  /**
   * 游戏消息同步到Telegram
   */
  @Post('sync-to-telegram')
  @ApiOperation({ summary: '游戏消息同步到Telegram' })
  @ApiBody({ type: SyncDto })
  @ApiResponse({ status: 200, description: '同步成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async syncToTelegram(@Body() syncDto: SyncDto) {
    try {
      this.logger.log(`接收到游戏消息同步请求: ${syncDto.messageId}`);
      
      // 验证必要参数
      if (!syncDto.content || !syncDto.fromUser || !syncDto.messageId) {
        throw new HttpException('缺少必要参数', HttpStatus.BAD_REQUEST);
      }

      // 这里应该调用消息队列处理
      // 暂时返回成功响应
      return BasicUtils.okResponse({
        messageId: syncDto.messageId,
        status: 'queued',
        message: '消息已加入处理队列',
      });
    } catch (error) {
      this.logger.error('游戏消息同步失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 发送消息到游戏聊天
   */
  @Post('send-message')
  @ApiOperation({ summary: '发送消息到游戏聊天' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        channelId: { type: 'number', description: '游戏频道ID' },
        message: { type: 'string', description: '消息内容' },
        fromUser: { type: 'string', description: '发送用户' },
      },
      required: ['channelId', 'message'],
    },
  })
  @ApiResponse({ status: 200, description: '发送成功' })
  async sendMessage(@Body() body: { channelId: number; message: string; fromUser?: string }) {
    try {
      const { channelId, message, fromUser } = body;
      
      if (!this.gameService.isValidChannelId(channelId)) {
        throw new HttpException('无效的频道ID', HttpStatus.BAD_REQUEST);
      }

      const success = await this.gameService.sendMessage(channelId, message, fromUser);
      
      if (success) {
        return BasicUtils.okResponse({
          channelId,
          message: '消息发送成功',
        });
      } else {
        throw new HttpException('消息发送失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      this.logger.error('发送消息到游戏失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取游戏聊天历史
   */
  @Get('chat-history')
  @ApiOperation({ summary: '获取游戏聊天历史' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getChatHistory(@Body() body: { channelId: number; limit?: number }) {
    try {
      const { channelId, limit = 50 } = body;
      
      if (!this.gameService.isValidChannelId(channelId)) {
        throw new HttpException('无效的频道ID', HttpStatus.BAD_REQUEST);
      }

      const messages = await this.gameService.getChatHistory(channelId, limit);
      
      return BasicUtils.okResponse({
        channelId,
        messages,
        count: messages.length,
      });
    } catch (error) {
      this.logger.error('获取游戏聊天历史失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 游戏服务健康检查
   */
  @Get('health')
  @ApiOperation({ summary: '游戏服务健康检查' })
  @ApiResponse({ status: 200, description: '服务正常' })
  async healthCheck() {
    try {
      const isConnected = await this.gameService.checkConnection();
      const apiInfo = this.gameService.getApiInfo();
      
      return BasicUtils.okResponse({
        status: isConnected ? 'healthy' : 'unhealthy',
        connection: isConnected,
        apiInfo,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('游戏服务健康检查失败:', error);
      throw new HttpException('健康检查失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取频道信息
   */
  @Get('channel-info')
  @ApiOperation({ summary: '获取游戏频道信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getChannelInfo(@Body() body: { channelId: number }) {
    try {
      const { channelId } = body;
      
      if (!this.gameService.isValidChannelId(channelId)) {
        throw new HttpException('无效的频道ID', HttpStatus.BAD_REQUEST);
      }

      const channelInfo = await this.gameService.getChannelInfo(channelId);
      
      if (channelInfo) {
        return BasicUtils.okResponse(channelInfo);
      } else {
        throw new HttpException('频道信息不存在', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      this.logger.error('获取频道信息失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
