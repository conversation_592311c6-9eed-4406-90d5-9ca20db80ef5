import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHello(): string {
    return 'Telegram-游戏聊天同步系统 v2.0 - 简化版';
  }

  async getHealthStatus() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      services: {
        mongodb: process.env.MONGODB_URI ? 'configured' : 'not_configured',
        redis: process.env.REDIS_HOST ? 'configured' : 'not_configured',
        telegram: process.env.TELEGRAM_BOT_TOKEN ? 'configured' : 'not_configured',
        gameApi: process.env.GAME_CHAT_API_URL ? 'configured' : 'not_configured',
      },
    };
  }

  getVersion() {
    return {
      version: '2.0.0',
      name: 'Telegram-游戏聊天同步系统',
      description: '简化版本，统一架构，高效同步',
      features: [
        '统一的同步模块',
        '简化的配置管理',
        '优化的消息处理',
        '减少的依赖关系',
        '统一的错误处理',
      ],
    };
  }

  async getSystemStatus() {
    const memoryUsage = process.memoryUsage();
    return {
      pid: process.pid,
      uptime: process.uptime(),
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
      },
      cpu: process.cpuUsage(),
      platform: process.platform,
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development',
    };
  }
}
