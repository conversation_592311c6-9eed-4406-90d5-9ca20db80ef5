import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

import * as dotenv from 'dotenv';
dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 配置 Swagger 文档
  const config = new DocumentBuilder()
    .setTitle('Telegram-游戏聊天同步系统')
    .setDescription('实现 Telegram 群组主题帖与游戏聊天窗口的双向消息同步，包含广告过滤管理功能')
    .setVersion('1.0')
    .addTag('api', '外部 API 接口')
    .addTag('telegram', 'Telegram 相关接口')
    .addTag('game', '游戏相关接口')
    .addTag('telegram-bot', 'Telegram Bot 管理接口')
    .addTag('admin', '广告过滤管理接口')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);

  const port = process.env.PORT || 3000;
  await app.listen(port, '0.0.0.0');

  console.log(`🚀 应用程序启动成功！`);
  console.log(`🌐 服务地址: http://localhost:${port}`);
  console.log(`📚 API 文档: http://localhost:${port}/api-docs`);
  console.log(`🔧 运行环境: ${process.env.NODE_ENV === 'development' ? '开发模式' : '生产模式'}`);
}

bootstrap();
