import { Logger } from '@nestjs/common';

/**
 * 简化的错误信息接口
 */
export interface SimpleErrorInfo {
  code: number;
  message: string;
  type: 'NETWORK' | 'VALIDATION' | 'BUSINESS' | 'SYSTEM';
  retryable: boolean;
  timestamp: string;
}

/**
 * 简化的错误处理工具类
 */
export class SimpleErrorUtils {
  private static readonly logger = new Logger(SimpleErrorUtils.name);

  /**
   * 格式化错误信息
   */
  static formatError(error: any, operation: string): SimpleErrorInfo {
    const timestamp = new Date().toISOString();
    
    // 网络错误
    if (this.isNetworkError(error)) {
      return {
        code: 503,
        message: `${operation}失败: 网络连接错误`,
        type: 'NETWORK',
        retryable: true,
        timestamp,
      };
    }
    
    // HTTP错误
    if (error.response?.status) {
      const status = error.response.status;
      return {
        code: status,
        message: `${operation}失败: HTTP ${status}`,
        type: status >= 500 ? 'SYSTEM' : 'BUSINESS',
        retryable: status >= 500,
        timestamp,
      };
    }
    
    // 验证错误
    if (this.isValidationError(error)) {
      return {
        code: 400,
        message: `${operation}失败: 参数验证错误`,
        type: 'VALIDATION',
        retryable: false,
        timestamp,
      };
    }
    
    // 默认系统错误
    return {
      code: 500,
      message: `${operation}失败: ${error.message || '未知错误'}`,
      type: 'SYSTEM',
      retryable: true,
      timestamp,
    };
  }

  /**
   * 记录错误日志
   */
  static logError(errorInfo: SimpleErrorInfo, context?: any): void {
    const message = `[${errorInfo.type}] ${errorInfo.message}`;
    
    switch (errorInfo.type) {
      case 'SYSTEM':
        this.logger.error(message, context);
        break;
      case 'NETWORK':
        this.logger.warn(message, context);
        break;
      default:
        this.logger.debug(message, context);
    }
  }

  /**
   * 获取重试延迟时间（秒）
   */
  static getRetryDelay(retryCount: number, baseDelay: number = 30): number {
    return Math.min(baseDelay * Math.pow(2, retryCount), 300);
  }

  /**
   * 创建错误响应
   */
  static createErrorResponse(errorInfo: SimpleErrorInfo) {
    return {
      success: false,
      code: errorInfo.code,
      message: errorInfo.message,
      type: errorInfo.type,
      retryable: errorInfo.retryable,
      timestamp: errorInfo.timestamp,
    };
  }

  /**
   * 判断是否为网络错误
   */
  private static isNetworkError(error: any): boolean {
    const networkCodes = ['ECONNREFUSED', 'ENOTFOUND', 'ETIMEDOUT', 'ECONNRESET'];
    return networkCodes.includes(error.code);
  }

  /**
   * 判断是否为验证错误
   */
  private static isValidationError(error: any): boolean {
    return error.name === 'ValidationError' || 
           error.message?.includes('validation') ||
           error.message?.includes('参数');
  }

  /**
   * 安全地执行异步操作
   */
  static async safeExecute<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = 3
  ): Promise<{ success: boolean; data?: T; error?: SimpleErrorInfo }> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await operation();
        return { success: true, data: result };
      } catch (error) {
        lastError = error;
        const errorInfo = this.formatError(error, operationName);
        
        // 如果不可重试或已达到最大重试次数，直接返回错误
        if (!errorInfo.retryable || attempt === maxRetries) {
          this.logError(errorInfo, { attempt: attempt + 1, maxRetries: maxRetries + 1 });
          return { success: false, error: errorInfo };
        }
        
        // 等待重试
        if (attempt < maxRetries) {
          const delay = this.getRetryDelay(attempt);
          this.logger.warn(`${operationName}失败，${delay}秒后重试 (${attempt + 1}/${maxRetries + 1})`);
          await this.sleep(delay * 1000);
        }
      }
    }
    
    // 理论上不会到达这里，但为了类型安全
    const errorInfo = this.formatError(lastError, operationName);
    return { success: false, error: errorInfo };
  }

  /**
   * 睡眠函数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
