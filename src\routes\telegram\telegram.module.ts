import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { MongooseModule } from '@nestjs/mongoose';
import { TelegramController } from './telegram.controller';
import { TelegramService } from './telegram.service';
import { TelegramToGameHandle } from './handler/telegram-to-game.handle';
import { SyncConfig, SyncConfigSchema } from '../../schemas/sync-config.schema';
import { MessageStatus, MessageStatusSchema } from '../../schemas/message-status.schema';
import { queues } from '../../constant/mq.constant';

@Module({
  imports: [
    HttpModule,
    BullModule.registerQueue({
      name: queues.TG_CHAT_SYNC_GAME_QUEUE,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      },
    }),
    MongooseModule.forFeature([
      { name: SyncConfig.name, schema: SyncConfigSchema },
      { name: MessageStatus.name, schema: MessageStatusSchema },
    ]),
  ],
  controllers: [TelegramController],
  providers: [TelegramService, TelegramToGameHandle],
  exports: [TelegramService],
})
export class TelegramModule {}
