import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { MongooseModule } from '@nestjs/mongoose';
import { RedisModule as NestRedisModule } from '@nestjs-modules/ioredis';
import { SyncController } from './sync.controller';
import { SyncService } from './sync.service';
import { MessageService } from './message.service';
import { RedisService } from './redis.service';
import { ConfigService } from './config.service';
import { SyncConfig, SyncConfigSchema } from '../schemas/sync-config.schema';
import { MessageStatus, MessageStatusSchema } from '../schemas/message-status.schema';
import { queues } from '../constant/mq.constant';

@Module({
  imports: [
    // Redis配置
    NestRedisModule.forRoot({
      type: 'single',
      url: `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || 6379}`,
      options: {
        password: process.env.REDIS_PASSWORD,
        maxRetriesPerRequest: 3,
      },
    }),
    // Redis队列配置
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
      },
    }),
    BullModule.registerQueue(
      { name: queues.MESSAGE_SYNC_QUEUE },
    ),
    // MongoDB模式
    MongooseModule.forFeature([
      { name: SyncConfig.name, schema: SyncConfigSchema },
      { name: MessageStatus.name, schema: MessageStatusSchema },
    ]),
  ],
  controllers: [SyncController],
  providers: [SyncService, MessageService, RedisService, ConfigService],
  exports: [SyncService, MessageService, RedisService, ConfigService],
})
export class SyncModule {}
