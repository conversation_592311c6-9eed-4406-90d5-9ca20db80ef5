import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SyncConfig } from '../schemas/sync-config.schema';

@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);
  private configCache = new Map<string, any>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  constructor(
    @InjectModel(SyncConfig.name) private readonly syncConfigModel: Model<SyncConfig>,
  ) {}

  /**
   * 根据Telegram群组ID获取配置
   */
  async getConfigByChatId(chatId: string): Promise<any> {
    const cacheKey = `telegram:${chatId}`;
    
    // 检查缓存
    if (this.isValidCache(cacheKey)) {
      return this.configCache.get(cacheKey);
    }

    try {
      const config = await this.syncConfigModel.findOne({
        telegramGroupId: chatId,
        enabled: true,
      }).exec();

      // 更新缓存
      this.updateCache(cacheKey, config);
      return config;
    } catch (error) {
      this.logger.error(`获取Telegram配置失败: ${chatId}`, error);
      return null;
    }
  }

  /**
   * 根据游戏频道ID获取配置
   */
  async getConfigByGameChannelId(channelId: string): Promise<any> {
    const cacheKey = `game:${channelId}`;
    
    // 检查缓存
    if (this.isValidCache(cacheKey)) {
      return this.configCache.get(cacheKey);
    }

    try {
      const config = await this.syncConfigModel.findOne({
        gameChatChannelId: parseInt(channelId),
        enabled: true,
      }).exec();

      // 更新缓存
      this.updateCache(cacheKey, config);
      return config;
    } catch (error) {
      this.logger.error(`获取游戏配置失败: ${channelId}`, error);
      return null;
    }
  }

  /**
   * 获取所有启用的配置
   */
  async getAllConfigs(): Promise<any[]> {
    const cacheKey = 'all_configs';
    
    // 检查缓存
    if (this.isValidCache(cacheKey)) {
      return this.configCache.get(cacheKey);
    }

    try {
      const configs = await this.syncConfigModel.find({
        enabled: true,
      }).sort({ priority: 1 }).exec();

      // 更新缓存
      this.updateCache(cacheKey, configs);
      return configs;
    } catch (error) {
      this.logger.error('获取所有配置失败', error);
      return [];
    }
  }

  /**
   * 创建或更新配置
   */
  async upsertConfig(configData: any): Promise<any> {
    try {
      const config = await this.syncConfigModel.findOneAndUpdate(
        { telegramGroupId: configData.telegramGroupId },
        configData,
        { upsert: true, new: true }
      ).exec();

      // 清除相关缓存
      this.clearRelatedCache(configData.telegramGroupId, configData.gameChatChannelId);
      
      return config;
    } catch (error) {
      this.logger.error('更新配置失败', error);
      throw error;
    }
  }

  /**
   * 批量更新配置
   */
  async bulkUpsertConfigs(configs: any[]): Promise<any[]> {
    const results: any[] = [];

    for (const configData of configs) {
      try {
        const result = await this.upsertConfig(configData);
        results.push(result);
      } catch (error) {
        this.logger.error(`批量更新配置失败: ${configData.telegramGroupId}`, error);
        results.push({ error: error.message, config: configData });
      }
    }

    return results;
  }

  /**
   * 批量验证配置
   */
  async validateConfigs(configs: any[]): Promise<any[]> {
    const results: any[] = [];

    for (const configData of configs) {
      try {
        const validation = this.validateConfig(configData);
        const result = {
          valid: validation.valid,
          errors: validation.errors,
          config: configData,
        };
        results.push(result);
      } catch (error) {
        results.push({ error: error.message, config: configData });
      }
    }

    return results;
  }

  /**
   * 删除配置
   */
  async deleteConfig(telegramGroupId: string): Promise<boolean> {
    try {
      const result = await this.syncConfigModel.deleteOne({
        telegramGroupId: telegramGroupId,
      }).exec();

      if (result.deletedCount > 0) {
        // 清除相关缓存
        this.clearRelatedCache(telegramGroupId);
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error(`删除配置失败: ${telegramGroupId}`, error);
      return false;
    }
  }

  /**
   * 启用/禁用配置
   */
  async toggleConfig(telegramGroupId: string, enabled: boolean): Promise<any> {
    try {
      const config = await this.syncConfigModel.findOneAndUpdate(
        { telegramGroupId: telegramGroupId },
        { enabled: enabled },
        { new: true }
      ).exec();

      // 清除相关缓存
      this.clearRelatedCache(telegramGroupId);
      
      return config;
    } catch (error) {
      this.logger.error(`切换配置状态失败: ${telegramGroupId}`, error);
      throw error;
    }
  }

  /**
   * 获取环境变量配置
   */
  getEnvConfig() {
    return {
      telegramBotToken: process.env.TELEGRAM_BOT_TOKEN,
      gameChatApiUrl: process.env.GAME_CHAT_API_URL,
      redisHost: process.env.REDIS_HOST || 'localhost',
      redisPort: parseInt(process.env.REDIS_PORT || '6379'),
      mongodbUri: process.env.MONGODB_URI,
      nodeEnv: process.env.NODE_ENV || 'development',
    };
  }

  /**
   * 验证配置完整性
   */
  validateConfig(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.telegramGroupId) {
      errors.push('Telegram群组ID不能为空');
    }

    if (!config.gameChatChannelId) {
      errors.push('游戏频道ID不能为空');
    }

    if (typeof config.priority !== 'number' || config.priority < 1) {
      errors.push('优先级必须是大于0的数字');
    }

    if (!config.description) {
      errors.push('描述不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors,
    };
  }

  /**
   * 检查缓存是否有效
   */
  private isValidCache(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    if (!expiry || Date.now() > expiry) {
      this.configCache.delete(key);
      this.cacheExpiry.delete(key);
      return false;
    }
    return this.configCache.has(key);
  }

  /**
   * 更新缓存
   */
  private updateCache(key: string, value: any): void {
    this.configCache.set(key, value);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL);
  }

  /**
   * 清除相关缓存
   */
  private clearRelatedCache(telegramGroupId?: string, gameChatChannelId?: number): void {
    // 清除所有配置缓存
    this.configCache.delete('all_configs');
    this.cacheExpiry.delete('all_configs');

    if (telegramGroupId) {
      this.configCache.delete(`telegram:${telegramGroupId}`);
      this.cacheExpiry.delete(`telegram:${telegramGroupId}`);
    }

    if (gameChatChannelId) {
      this.configCache.delete(`game:${gameChatChannelId}`);
      this.cacheExpiry.delete(`game:${gameChatChannelId}`);
    }
  }

  /**
   * 清除所有缓存
   */
  clearAllCache(): void {
    this.configCache.clear();
    this.cacheExpiry.clear();
    this.logger.log('已清除所有配置缓存');
  }
}
