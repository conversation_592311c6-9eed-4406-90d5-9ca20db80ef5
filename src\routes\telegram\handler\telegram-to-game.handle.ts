import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { jobs, queues } from '../../../constant/mq.constant';
import { Job } from 'bull';
import { TelegramService } from '../telegram.service';

@Injectable()
@Processor(queues.TG_CHAT_SYNC_GAME_QUEUE)
export class TelegramToGameHandle {
  private readonly logger: Logger = new Logger(TelegramToGameHandle.name);

  constructor(private readonly telegramService: TelegramService) {}

  @Process(jobs.TG_CHAT_SYNC_GAME_JOB)
  async handleTask(job: Job) {
    const { message, telegramGroupId } = job.data;
    await this.telegramService.handleSyncToGame(message, telegramGroupId);
  }
}
