import { Logger } from '@nestjs/common';
import { httpCode, ErrorType, ErrorSeverity } from '../constant/http.constant';

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  type: ErrorType;
  severity: ErrorSeverity;
  code: number;
  message: string;
  details?: any;
  context?: string;
  timestamp?: Date;
  retryable?: boolean;
}

/**
 * 错误处理工具类
 */
export class ErrorUtils {
  /**
   * 格式化错误信息
   * @param error 原始错误
   * @param context 错误上下文
   * @param additionalInfo 额外信息
   */
  static formatError(error: any, context?: string, additionalInfo?: any): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      code: httpCode.UNKNOWN_ERROR.code,
      message: '未知错误',
      context,
      timestamp: new Date(),
      retryable: false,
    };

    // 根据错误类型进行分类
    if (this.isTelegramApiError(error)) {
      errorInfo.type = ErrorType.PERMISSION;
      errorInfo.severity = this.getTelegramErrorSeverity(error);
      errorInfo.code = this.getTelegramErrorCode(error);
      errorInfo.message = this.getTelegramErrorMessage(error);
      errorInfo.retryable = this.isTelegramErrorRetryable(error);
    } else if (this.isValidationError(error)) {
      errorInfo.type = ErrorType.VALIDATION;
      errorInfo.severity = ErrorSeverity.LOW;
      errorInfo.code = httpCode.MESSAGE_VALIDATION_ERROR.code;
      errorInfo.message = `数据验证失败: ${error.message || '格式不正确'}`;
      errorInfo.retryable = false;
    } else if (this.isConfigError(error)) {
      errorInfo.type = ErrorType.CONFIG;
      errorInfo.severity = ErrorSeverity.HIGH;
      errorInfo.code = httpCode.CONFIG_ERROR.code;
      errorInfo.message = `配置错误: ${error.message || '配置项缺失或无效'}`;
      errorInfo.retryable = false;
    } else if (this.isNetworkError(error)) {
      errorInfo.type = ErrorType.NETWORK;
      errorInfo.severity = ErrorSeverity.MEDIUM;
      errorInfo.code = httpCode.TELEGRAM_NETWORK_ERROR.code;
      errorInfo.message = this.getNetworkErrorMessage(error);
      errorInfo.retryable = true;
    }

    // 添加详细信息
    errorInfo.details = {
      originalError: error.message || String(error),
      stack: error.stack,
      ...additionalInfo,
    };

    return errorInfo;
  }

  /**
   * 记录错误日志
   * @param logger 日志记录器
   * @param errorInfo 错误信息
   * @param operationId 操作ID（如消息ID、任务ID等）
   */
  static logError(logger: Logger, errorInfo: ErrorInfo, operationId?: string) {
    const logPrefix = operationId ? `[${operationId}]` : '';
    const severityIcon = this.getSeverityIcon(errorInfo.severity);
    const retryableText = errorInfo.retryable ? '可重试' : '不可重试';

    const logMessage = `${logPrefix} ${severityIcon} [${errorInfo.type}] ${errorInfo.message} (${retryableText})`;

    switch (errorInfo.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error(logMessage);
        if (errorInfo.details) {
          logger.error(`错误详情: ${JSON.stringify(errorInfo.details, null, 2)}`);
        }
        break;
      case ErrorSeverity.HIGH:
        logger.error(logMessage);
        if (errorInfo.details?.originalError) {
          logger.error(`原始错误: ${errorInfo.details.originalError}`);
        }
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn(logMessage);
        break;
      case ErrorSeverity.LOW:
        logger.log(logMessage);
        break;
    }

    // 如果有上下文信息，单独记录
    if (errorInfo.context) {
      logger.debug(`错误上下文: ${errorInfo.context}`);
    }
  }

  /**
   * 判断是否为网络错误
   */
  private static isNetworkError(error: any): boolean {
    const networkCodes = ['ECONNABORTED', 'ENOTFOUND', 'ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED'];
    const timeoutPatterns = ['timeout', 'TIMEOUT', 'time out', 'Request timeout'];
    return networkCodes.includes(error.code) || 
    timeoutPatterns.some(pattern => error.message?.includes(pattern)) ||
    error.name === 'TimeoutError';
  }

  /**
   * 判断是否为Telegram API错误
   */
  private static isTelegramApiError(error: any): boolean {
    return error.response?.data?.error_code || error.response?.status;
  }

  /**
   * 判断是否为验证错误
   */
  private static isValidationError(error: any): boolean {
    return (
      error.message?.includes('验证') ||
      error.message?.includes('格式') ||
      error.message?.includes('缺失') ||
      error.name === 'ValidationError'
    );
  }

  /**
   * 判断是否为配置错误
   */
  private static isConfigError(error: any): boolean {
    return (
      error.message?.includes('配置') ||
      error.message?.includes('环境变量') ||
      error.message?.includes('GAME_CHAT_CHANNEL_ID')
    );
  }

  /**
   * 获取网络错误消息
   */
  private static getNetworkErrorMessage(error: any): string {
    const errorMap: Record<string, string> = {
      ECONNABORTED: '连接超时，请检查网络连接',
      ENOTFOUND: 'DNS解析失败，请检查网络设置',
      ECONNRESET: '连接被重置，请稍后重试',
      ETIMEDOUT: '请求超时，请检查网络状况',
      ECONNREFUSED: '连接被拒绝，请检查服务状态',
    };

    return errorMap[error.code] || `网络错误: ${error.message || '未知网络问题'}`;
  }

  /**
   * 获取Telegram错误严重级别
   */
  private static getTelegramErrorSeverity(error: any): ErrorSeverity {
    const status = error.response?.status;
    if (status === 403) return ErrorSeverity.HIGH; // 权限问题
    if (status === 429) return ErrorSeverity.MEDIUM; // 频率限制
    if (status >= 500) return ErrorSeverity.HIGH; // 服务器错误
    return ErrorSeverity.MEDIUM;
  }

  /**
   * 获取Telegram错误码
   */
  private static getTelegramErrorCode(error: any): number {
    const status = error.response?.status;
    if (status === 403) return httpCode.TELEGRAM_PERMISSION_ERROR.code;
    if (status === 429) return httpCode.TELEGRAM_RATE_LIMIT.code;
    return httpCode.TELEGRAM_API_ERROR.code;
  }

  /**
   * 获取Telegram错误消息
   */
  private static getTelegramErrorMessage(error: any): string {
    const status = error.response?.status;
    const description = error.response?.data?.description;
    const errorCode = error.response?.data?.error_code;

    let message = 'Telegram API调用失败';

    if (status === 403) {
      message = 'Bot权限不足，请检查Bot是否已加入群组并有发送消息权限';
    } else if (status === 429) {
      const retryAfter = error.response?.data?.parameters?.retry_after;
      message = `触发频率限制${retryAfter ? `，请等待${retryAfter}秒后重试` : ''}`;
    } else if (status === 400) {
      message = `请求参数错误: ${description || '参数格式不正确'}`;
    } else if (status >= 500) {
      message = `Telegram服务器错误 (${status}): ${description || '服务暂时不可用'}`;
    }

    if (errorCode) {
      message += ` (错误码: ${errorCode})`;
    }

    return message;
  }

  /**
   * 判断Telegram错误是否可重试
   */
  private static isTelegramErrorRetryable(error: any): boolean {
    const status = error.response?.status;
    return status === 429 || status >= 500; // 频率限制和服务器错误可重试
  }

  /**
   * 获取严重级别图标
   */
  private static getSeverityIcon(severity: ErrorSeverity): string {
    const icons: Record<ErrorSeverity, string> = {
      [ErrorSeverity.LOW]: '🔵',
      [ErrorSeverity.MEDIUM]: '🟡',
      [ErrorSeverity.HIGH]: '🟠',
      [ErrorSeverity.CRITICAL]: '🔴',
    };
    return icons[severity];
  }

  /**
   * 创建用户友好的错误响应
   * @param errorInfo 错误信息
   */
  static createErrorResponse(errorInfo: ErrorInfo) {
    return {
      code: errorInfo.code,
      msg: errorInfo.message,
      type: errorInfo.type,
      retryable: errorInfo.retryable,
      timestamp: errorInfo.timestamp,
    };
  }

  /**
   * 从错误中提取重试延迟时间
   * @param error 错误对象
   * @returns 延迟时间（毫秒）
   */
  static getRetryDelay(error: any): number {
    // Telegram频率限制
    if (error.response?.status === 429) {
      const retryAfter = error.response?.data?.parameters?.retry_after;
      return retryAfter ? retryAfter * 1000 : 60000; // 默认1分钟
    }

    // 网络错误使用指数退避
    if (this.isNetworkError(error)) {
      return Math.min(30000, 1000 * Math.pow(2, Math.random() * 3)); // 1-8秒随机延迟
    }

    return 5000; // 默认5秒
  }
}
