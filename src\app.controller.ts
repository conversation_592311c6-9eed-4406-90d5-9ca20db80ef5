import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';
import { BasicUtils } from './utils/basic.utils';

@ApiTags('app')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '应用欢迎信息' })
  @ApiResponse({ status: 200, description: '返回应用信息' })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '服务健康状态' })
  async healthCheck() {
    try {
      const health = await this.appService.getHealthStatus();
      return BasicUtils.okResponse(health);
    } catch (error) {
      return BasicUtils.errorResponseWithCodeMsg(500, '健康检查失败');
    }
  }

  @Get('version')
  @ApiOperation({ summary: '获取版本信息' })
  @ApiResponse({ status: 200, description: '返回版本信息' })
  getVersion() {
    const version = this.appService.getVersion();
    return BasicUtils.okResponse(version);
  }

  @Get('status')
  @ApiOperation({ summary: '获取系统状态' })
  @ApiResponse({ status: 200, description: '返回系统运行状态' })
  async getStatus() {
    try {
      const status = await this.appService.getSystemStatus();
      return BasicUtils.okResponse(status);
    } catch (error) {
      return BasicUtils.errorResponseWithCodeMsg(500, '获取状态失败');
    }
  }
}
