import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class GameService {
  private readonly logger = new Logger(GameService.name);
  private readonly gameApiUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.gameApiUrl = process.env.GAME_CHAT_API_URL || 'http://localhost:8080';
  }

  /**
   * 发送消息到游戏聊天
   */
  async sendMessage(channelId: number, message: string, fromUser?: string): Promise<boolean> {
    try {
      const payload = {
        channelId,
        message,
        fromUser: fromUser || 'Telegram',
        timestamp: Date.now(),
      };

      const response: AxiosResponse = await firstValueFrom(
        this.httpService.post(`${this.gameApiUrl}/api/chat/send`, payload, {
          timeout: 5000,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );

      if (response.status === 200) {
        this.logger.log(`消息发送成功到游戏频道 ${channelId}: ${message.substring(0, 50)}...`);
        return true;
      } else {
        this.logger.warn(`游戏API返回非200状态: ${response.status}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送消息到游戏失败 (频道 ${channelId}):`, error);
      return false;
    }
  }

  /**
   * 获取游戏聊天历史
   */
  async getChatHistory(channelId: number, limit: number = 50): Promise<any[]> {
    try {
      const response: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.gameApiUrl}/api/chat/history`, {
          params: { channelId, limit },
          timeout: 5000,
        })
      );

      if (response.status === 200 && response.data) {
        return response.data.messages || [];
      }
      
      return [];
    } catch (error) {
      this.logger.error(`获取游戏聊天历史失败 (频道 ${channelId}):`, error);
      return [];
    }
  }

  /**
   * 检查游戏API连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.gameApiUrl}/api/health`, {
          timeout: 3000,
        })
      );

      return response.status === 200;
    } catch (error) {
      this.logger.error('游戏API连接检查失败:', error);
      return false;
    }
  }

  /**
   * 获取游戏频道信息
   */
  async getChannelInfo(channelId: number): Promise<any> {
    try {
      const response: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.gameApiUrl}/api/chat/channel/${channelId}`, {
          timeout: 5000,
        })
      );

      if (response.status === 200) {
        return response.data;
      }
      
      return null;
    } catch (error) {
      this.logger.error(`获取游戏频道信息失败 (频道 ${channelId}):`, error);
      return null;
    }
  }

  /**
   * 格式化消息内容
   */
  formatMessage(content: string, fromUser?: string): string {
    if (!content) return '';

    // 移除Telegram特殊格式
    let formatted = content
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体
      .replace(/__(.*?)__/g, '$1')     // 移除下划线
      .replace(/`(.*?)`/g, '$1')       // 移除代码格式
      .replace(/\[(.*?)\]\(.*?\)/g, '$1'); // 移除链接格式，保留文本

    // 添加用户前缀
    if (fromUser) {
      formatted = `[${fromUser}] ${formatted}`;
    }

    return formatted.trim();
  }

  /**
   * 验证频道ID
   */
  isValidChannelId(channelId: any): boolean {
    const id = parseInt(channelId);
    return !isNaN(id) && id > 0;
  }

  /**
   * 获取API配置信息
   */
  getApiInfo() {
    return {
      apiUrl: this.gameApiUrl,
      configured: !!process.env.GAME_CHAT_API_URL,
    };
  }

  /**
   * 处理同步到Telegram的消息
   */
  async handleSyncToTelegram(data: { message: any; targetMapping: any }): Promise<void> {
    const { message, targetMapping } = data;

    try {
      this.logger.log(`处理游戏消息同步到Telegram: ${message?.messageId}`);

      // 这里应该实现具体的同步逻辑
      // 暂时只记录日志
      this.logger.log(`消息内容: ${message?.content}`);
      this.logger.log(`目标群组: ${targetMapping?.telegramGroupId}`);

    } catch (error) {
      this.logger.error('处理游戏消息同步失败:', error);
      throw error;
    }
  }
}
