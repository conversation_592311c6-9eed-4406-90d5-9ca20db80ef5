import { Controller, Post, Body, Get, Logger, HttpException, HttpStatus, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiQuery } from '@nestjs/swagger';
import { TelegramService } from './telegram.service';
import { SyncDto } from '../../dto/sync.dto';
import { BasicUtils } from '../../utils/basic.utils';

@ApiTags('Telegram')
@Controller('telegram')
export class TelegramController {
  private readonly logger = new Logger(TelegramController.name);

  constructor(private readonly telegramService: TelegramService) {}

  /**
   * Telegram消息同步到游戏
   */
  @Post('sync-to-game')
  @ApiOperation({ summary: 'Telegram消息同步到游戏' })
  @ApiBody({ type: SyncDto })
  @ApiResponse({ status: 200, description: '同步成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async syncToGame(@Body() syncDto: SyncDto) {
    try {
      this.logger.log(`接收到Telegram消息同步请求: ${syncDto.messageId}`);
      
      // 验证必要参数
      if (!syncDto.content || !syncDto.fromUser || !syncDto.messageId) {
        throw new HttpException('缺少必要参数', HttpStatus.BAD_REQUEST);
      }

      // 这里应该调用消息队列处理
      // 暂时返回成功响应
      return BasicUtils.okResponse({
        messageId: syncDto.messageId,
        status: 'queued',
        message: '消息已加入处理队列',
      });
    } catch (error) {
      this.logger.error('Telegram消息同步失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 发送消息到Telegram群组
   */
  @Post('send-message')
  @ApiOperation({ summary: '发送消息到Telegram群组' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        chatId: { type: 'string', description: 'Telegram群组ID' },
        message: { type: 'string', description: '消息内容' },
        threadId: { type: 'string', description: '线程ID（可选）' },
        fromUser: { type: 'string', description: '发送用户（可选）' },
      },
      required: ['chatId', 'message'],
    },
  })
  @ApiResponse({ status: 200, description: '发送成功' })
  async sendMessage(@Body() body: { 
    chatId: string; 
    message: string; 
    threadId?: string; 
    fromUser?: string;
  }) {
    try {
      const { chatId, message, threadId, fromUser } = body;
      
      if (!this.telegramService.isValidChatId(chatId)) {
        throw new HttpException('无效的群组ID', HttpStatus.BAD_REQUEST);
      }

      if (threadId && !this.telegramService.isValidThreadId(threadId)) {
        throw new HttpException('无效的线程ID', HttpStatus.BAD_REQUEST);
      }

      const formattedMessage = this.telegramService.formatMessage(message, fromUser);
      const success = await this.telegramService.sendMessage(chatId, formattedMessage, threadId);
      
      if (success) {
        return BasicUtils.okResponse({
          chatId,
          threadId,
          message: '消息发送成功',
        });
      } else {
        throw new HttpException('消息发送失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      this.logger.error('发送消息到Telegram失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取群组信息
   */
  @Get('chat-info')
  @ApiOperation({ summary: '获取Telegram群组信息' })
  @ApiQuery({ name: 'chatId', description: 'Telegram群组ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getChatInfo(@Query('chatId') chatId: string) {
    try {
      if (!this.telegramService.isValidChatId(chatId)) {
        throw new HttpException('无效的群组ID', HttpStatus.BAD_REQUEST);
      }

      const chatInfo = await this.telegramService.getChatInfo(chatId);
      
      if (chatInfo) {
        return BasicUtils.okResponse(chatInfo);
      } else {
        throw new HttpException('群组信息不存在', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      this.logger.error('获取群组信息失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Telegram服务健康检查
   */
  @Get('health')
  @ApiOperation({ summary: 'Telegram服务健康检查' })
  @ApiResponse({ status: 200, description: '服务正常' })
  async healthCheck() {
    try {
      const botStatus = await this.telegramService.checkBotStatus();
      const configInfo = this.telegramService.getConfigInfo();
      
      let botInfo = null;
      if (botStatus) {
        botInfo = await this.telegramService.getBotInfo();
      }
      
      return BasicUtils.okResponse({
        status: botStatus ? 'healthy' : 'unhealthy',
        botStatus,
        botInfo,
        configInfo,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Telegram服务健康检查失败:', error);
      throw new HttpException('健康检查失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 发送文件到Telegram
   */
  @Post('send-document')
  @ApiOperation({ summary: '发送文件到Telegram群组' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        chatId: { type: 'string', description: 'Telegram群组ID' },
        document: { type: 'string', description: '文件内容或URL' },
        caption: { type: 'string', description: '文件说明（可选）' },
        threadId: { type: 'string', description: '线程ID（可选）' },
      },
      required: ['chatId', 'document'],
    },
  })
  @ApiResponse({ status: 200, description: '发送成功' })
  async sendDocument(@Body() body: { 
    chatId: string; 
    document: any; 
    caption?: string; 
    threadId?: string;
  }) {
    try {
      const { chatId, document, caption, threadId } = body;
      
      if (!this.telegramService.isValidChatId(chatId)) {
        throw new HttpException('无效的群组ID', HttpStatus.BAD_REQUEST);
      }

      if (threadId && !this.telegramService.isValidThreadId(threadId)) {
        throw new HttpException('无效的线程ID', HttpStatus.BAD_REQUEST);
      }

      const success = await this.telegramService.sendDocument(chatId, document, caption, threadId);
      
      if (success) {
        return BasicUtils.okResponse({
          chatId,
          threadId,
          message: '文件发送成功',
        });
      } else {
        throw new HttpException('文件发送失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      this.logger.error('发送文件到Telegram失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
