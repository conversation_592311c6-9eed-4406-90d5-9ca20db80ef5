import { httpCode } from '../constant/http.constant';
import { ErrorInfo } from './error.utils';

/**
 * @auther bc1pAzerty
 * @date 2024/10/12 12:17
 */
export class BasicUtils {
  /**
   * 校验钱包地址
   * @param address
   */
  static checkAddressType(address: string) {
    return address.startsWith('bc1p');
  }

  static okResponse<T>(data?: T) {
    return {
      code: httpCode.OK.code,
      msg: httpCode.OK.msg,
      data: data,
    };
  }

  static errorResponse<T>(error: { code: number; msg: string }, data?: T) {
    return {
      code: error.code,
      msg: error.msg,
      data: data,
    };
  }

  static errorResponseWithCodeMsg<T>(code: number, msg: string, data?: T) {
    return {
      code: code,
      msg: msg,
      data: data,
    };
  }

  /**
   * 创建基于错误信息的响应
   * @param errorInfo 错误信息对象
   */
  static createErrorResponse(errorInfo: ErrorInfo) {
    return {
      code: errorInfo.code,
      msg: errorInfo.message,
      type: errorInfo.type,
      retryable: errorInfo.retryable,
      timestamp: errorInfo.timestamp,
    };
  }

  static isResponseOk(resp: any) {
    return resp.code == httpCode.OK.code;
  }

  static unisatResponseOK(resp: any) {
    return resp.code == httpCode.UNISAT_OK.code;
  }
}
