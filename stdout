This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.env.example
.env.test
.prettierrc
config/ad-filter-default-config.json
config/multi-group-mappings.json
config/multi-group-mappings.online.json
config/multi-group-mappings.test.json
config/README.md
eslint.config.mjs
LICENSE
nest-cli.json
package.json
README.md
src/app.controller.ts
src/app.module.ts
src/app.service.ts
src/constant/http.constant.ts
src/constant/magic.constant.ts
src/constant/mq.constant.ts
src/dto/sync.dto.ts
src/enum/message-type.enum.ts
src/main.ts
src/message/message.module.ts
src/message/message.service.ts
src/redis/redis.module.ts
src/redis/redis.service.ts
src/routes/game/game.controller.ts
src/routes/game/game.module.ts
src/routes/game/game.service.ts
src/routes/game/handler/game-to-telegram.handle.ts
src/routes/multi-group-config/multi-group-config.controller.ts
src/routes/multi-group-config/multi-group-config.module.ts
src/routes/multi-group-config/multi-group-config.service.ts
src/routes/telegram-bot/telegram-bot.controller.ts
src/routes/telegram-bot/telegram-bot.module.ts
src/routes/telegram-bot/telegram-bot.service.ts
src/routes/telegram/handler/telegram-to-game.handle.ts
src/routes/telegram/telegram.controller.ts
src/routes/telegram/telegram.module.ts
src/routes/telegram/telegram.service.ts
src/schemas/ad-filter-config.schema.ts
src/schemas/data-push-config.schema.ts
src/schemas/message-status.schema.ts
src/schemas/multi-group-config.schema.ts
src/schemas/system-config.schema.ts
src/utils/basic.utils.ts
src/utils/error.utils.ts
tsconfig.build.json
tsconfig.json
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="config/ad-filter-default-config.json">
{
  "enabled": true,
  "keywords": [
    "免费领取",
    "限时优惠",
    "立即购买",
    "点击链接",
    "扫码关注",
    "加微信",
    "加QQ",
    "联系客服",
    "代理招募",
    "兼职赚钱",
    "投资理财",
    "股票推荐",
    "彩票中奖",
    "贷款办理",
    "信用卡办理",
    "代练",
    "代打",
    "充值优惠",
    "游戏币出售",
    "装备交易",
    "外挂",
    "脚本",
    "辅助工具",
    "刷金币",
    "刷等级",
    "金币代刷",
    "等级代练",
    "账号代练",
    "游戏代练",
    "发广告",
    "推广",
    "营销",
    "宣传",
    "招代理",
    "赚钱机会",
    "致富秘籍",
    "暴富",
    "躺赚",
    "日入",
    "月入",
    "年入",
    "轻松赚钱",
    "在家赚钱",
    "网络兼职",
    "中奖了",
    "恭喜中奖",
    "幸运用户",
    "特等奖",
    "一等奖",
    "领奖",
    "奖金",
    "红包雨",
    "现金奖励",
    "提现",
    "美女",
    "约会",
    "交友",
    "陪聊",
    "视频聊天",
    "赌博",
    "博彩",
    "彩票",
    "六合彩",
    "时时彩",
    "减肥",
    "丰胸",
    "壮阳",
    "治疗",
    "偏方",
    "神药",
    "特效药",
    "包治",
    "根治",
    "速效"
  ],
  "urlPatterns": [
    "https?:\\/\\/[^\\s]+",
    "www\\.[^\\s]+",
    "[a-zA-Z0-9-]+\\.(com|cn|net|org|info|biz|co|me|io|tv|cc|top|xyz|club|vip)([^\\s]*)",
    "[a-zA-Z0-9-]+\\.(tk|ml|ga|cf)([^\\s]*)"
  ],
  "contactPatterns": [
    "微信[：:：\\s]*[a-zA-Z0-9_-]{3,}",
    "QQ[：:：\\s]*[0-9]{5,}",
    "电话[：:：\\s]*[0-9-+\\s]{7,}",
    "手机[：:：\\s]*[0-9-+\\s]{7,}",
    "(?<!\\d)[1][3-9][0-9]{9}(?!\\d)",
    "(?<!\\d)[0-9]{3}-[0-9]{4}-[0-9]{4}(?!\\d)",
    "(?<!\\d)[0-9]{11}(?!\\d)",
    "联系[：:：\\s]*[a-zA-Z0-9_-]+",
    "咨询[：:：\\s]*[a-zA-Z0-9_-]+"
  ],
  "repeatThreshold": 3,
  "adThreshold": 0.3,
  "enableCapsDetection": true,
  "whitelist": [
    "游戏攻略",
    "游戏心得",
    "游戏分享",
    "游戏讨论",
    "装备分享",
    "技能分享",
    "经验分享",
    "心得体会",
    "免费游戏",
    "免费活动",
    "官方活动",
    "系统奖励",
    "每日签到",
    "登录奖励",
    "任务奖励",
    "成就奖励"
  ],
  "_metadata": {
    "version": "1.0.0",
    "description": "广告过滤默认配置文件",
    "lastUpdated": "2025-06-06T00:00:00.000Z",
    "note": "此文件包含系统默认的广告过滤配置，请勿直接修改。如需自定义配置，请使用 ad-filter-config.json 文件。"
  }
}
</file>

<file path="config/multi-group-mappings.test.json">
[
  {
    "telegramGroupId": "@grassPush",
    "gameChatChannelId": 1001,
    "telegramThreadId": "183",
    "telegramListenThreadIds": ["1", "8"],
    "enabled": true,
    "priority": 1,
    "description": "1001推送群组",
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": false,
      "allowEmpty": false,
      "maxMessageLength": 1000
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@testFractalPush",
    "gameChatChannelId": 1002,
    "enabled": true,
    "priority": 2,
    "description": "1002推送群组",
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": true,
      "allowEmpty": false,
      "maxMessageLength": 500
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  }
]
</file>

<file path="LICENSE">
GNU GENERAL PUBLIC LICENSE
                       Version 3, 29 June 2007

 Copyright (C) 2007 Free Software Foundation, Inc. <https://fsf.org/>
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.

                            Preamble

  The GNU General Public License is a free, copyleft license for
software and other kinds of works.

  The licenses for most software and other practical works are designed
to take away your freedom to share and change the works.  By contrast,
the GNU General Public License is intended to guarantee your freedom to
share and change all versions of a program--to make sure it remains free
software for all its users.  We, the Free Software Foundation, use the
GNU General Public License for most of our software; it applies also to
any other work released this way by its authors.  You can apply it to
your programs, too.

  When we speak of free software, we are referring to freedom, not
price.  Our General Public Licenses are designed to make sure that you
have the freedom to distribute copies of free software (and charge for
them if you wish), that you receive source code or can get it if you
want it, that you can change the software or use pieces of it in new
free programs, and that you know you can do these things.

  To protect your rights, we need to prevent others from denying you
these rights or asking you to surrender the rights.  Therefore, you have
certain responsibilities if you distribute copies of the software, or if
you modify it: responsibilities to respect the freedom of others.

  For example, if you distribute copies of such a program, whether
gratis or for a fee, you must pass on to the recipients the same
freedoms that you received.  You must make sure that they, too, receive
or can get the source code.  And you must show them these terms so they
know their rights.

  Developers that use the GNU GPL protect your rights with two steps:
(1) assert copyright on the software, and (2) offer you this License
giving you legal permission to copy, distribute and/or modify it.

  For the developers' and authors' protection, the GPL clearly explains
that there is no warranty for this free software.  For both users' and
authors' sake, the GPL requires that modified versions be marked as
changed, so that their problems will not be attributed erroneously to
authors of previous versions.

  Some devices are designed to deny users access to install or run
modified versions of the software inside them, although the manufacturer
can do so.  This is fundamentally incompatible with the aim of
protecting users' freedom to change the software.  The systematic
pattern of such abuse occurs in the area of products for individuals to
use, which is precisely where it is most unacceptable.  Therefore, we
have designed this version of the GPL to prohibit the practice for those
products.  If such problems arise substantially in other domains, we
stand ready to extend this provision to those domains in future versions
of the GPL, as needed to protect the freedom of users.

  Finally, every program is threatened constantly by software patents.
States should not allow patents to restrict development and use of
software on general-purpose computers, but in those that do, we wish to
avoid the special danger that patents applied to a free program could
make it effectively proprietary.  To prevent this, the GPL assures that
patents cannot be used to render the program non-free.

  The precise terms and conditions for copying, distribution and
modification follow.

                       TERMS AND CONDITIONS

  0. Definitions.

  "This License" refers to version 3 of the GNU General Public License.

  "Copyright" also means copyright-like laws that apply to other kinds of
works, such as semiconductor masks.

  "The Program" refers to any copyrightable work licensed under this
License.  Each licensee is addressed as "you".  "Licensees" and
"recipients" may be individuals or organizations.

  To "modify" a work means to copy from or adapt all or part of the work
in a fashion requiring copyright permission, other than the making of an
exact copy.  The resulting work is called a "modified version" of the
earlier work or a work "based on" the earlier work.

  A "covered work" means either the unmodified Program or a work based
on the Program.

  To "propagate" a work means to do anything with it that, without
permission, would make you directly or secondarily liable for
infringement under applicable copyright law, except executing it on a
computer or modifying a private copy.  Propagation includes copying,
distribution (with or without modification), making available to the
public, and in some countries other activities as well.

  To "convey" a work means any kind of propagation that enables other
parties to make or receive copies.  Mere interaction with a user through
a computer network, with no transfer of a copy, is not conveying.

  An interactive user interface displays "Appropriate Legal Notices"
to the extent that it includes a convenient and prominently visible
feature that (1) displays an appropriate copyright notice, and (2)
tells the user that there is no warranty for the work (except to the
extent that warranties are provided), that licensees may convey the
work under this License, and how to view a copy of this License.  If
the interface presents a list of user commands or options, such as a
menu, a prominent item in the list meets this criterion.

  1. Source Code.

  The "source code" for a work means the preferred form of the work
for making modifications to it.  "Object code" means any non-source
form of a work.

  A "Standard Interface" means an interface that either is an official
standard defined by a recognized standards body, or, in the case of
interfaces specified for a particular programming language, one that
is widely used among developers working in that language.

  The "System Libraries" of an executable work include anything, other
than the work as a whole, that (a) is included in the normal form of
packaging a Major Component, but which is not part of that Major
Component, and (b) serves only to enable use of the work with that
Major Component, or to implement a Standard Interface for which an
implementation is available to the public in source code form.  A
"Major Component", in this context, means a major essential component
(kernel, window system, and so on) of the specific operating system
(if any) on which the executable work runs, or a compiler used to
produce the work, or an object code interpreter used to run it.

  The "Corresponding Source" for a work in object code form means all
the source code needed to generate, install, and (for an executable
work) run the object code and to modify the work, including scripts to
control those activities.  However, it does not include the work's
System Libraries, or general-purpose tools or generally available free
programs which are used unmodified in performing those activities but
which are not part of the work.  For example, Corresponding Source
includes interface definition files associated with source files for
the work, and the source code for shared libraries and dynamically
linked subprograms that the work is specifically designed to require,
such as by intimate data communication or control flow between those
subprograms and other parts of the work.

  The Corresponding Source need not include anything that users
can regenerate automatically from other parts of the Corresponding
Source.

  The Corresponding Source for a work in source code form is that
same work.

  2. Basic Permissions.

  All rights granted under this License are granted for the term of
copyright on the Program, and are irrevocable provided the stated
conditions are met.  This License explicitly affirms your unlimited
permission to run the unmodified Program.  The output from running a
covered work is covered by this License only if the output, given its
content, constitutes a covered work.  This License acknowledges your
rights of fair use or other equivalent, as provided by copyright law.

  You may make, run and propagate covered works that you do not
convey, without conditions so long as your license otherwise remains
in force.  You may convey covered works to others for the sole purpose
of having them make modifications exclusively for you, or provide you
with facilities for running those works, provided that you comply with
the terms of this License in conveying all material for which you do
not control copyright.  Those thus making or running the covered works
for you must do so exclusively on your behalf, under your direction
and control, on terms that prohibit them from making any copies of
your copyrighted material outside their relationship with you.

  Conveying under any other circumstances is permitted solely under
the conditions stated below.  Sublicensing is not allowed; section 10
makes it unnecessary.

  3. Protecting Users' Legal Rights From Anti-Circumvention Law.

  No covered work shall be deemed part of an effective technological
measure under any applicable law fulfilling obligations under article
11 of the WIPO copyright treaty adopted on 20 December 1996, or
similar laws prohibiting or restricting circumvention of such
measures.

  When you convey a covered work, you waive any legal power to forbid
circumvention of technological measures to the extent such circumvention
is effected by exercising rights under this License with respect to
the covered work, and you disclaim any intention to limit operation or
modification of the work as a means of enforcing, against the work's
users, your or third parties' legal rights to forbid circumvention of
technological measures.

  4. Conveying Verbatim Copies.

  You may convey verbatim copies of the Program's source code as you
receive it, in any medium, provided that you conspicuously and
appropriately publish on each copy an appropriate copyright notice;
keep intact all notices stating that this License and any
non-permissive terms added in accord with section 7 apply to the code;
keep intact all notices of the absence of any warranty; and give all
recipients a copy of this License along with the Program.

  You may charge any price or no price for each copy that you convey,
and you may offer support or warranty protection for a fee.

  5. Conveying Modified Source Versions.

  You may convey a work based on the Program, or the modifications to
produce it from the Program, in the form of source code under the
terms of section 4, provided that you also meet all of these conditions:

    a) The work must carry prominent notices stating that you modified
    it, and giving a relevant date.

    b) The work must carry prominent notices stating that it is
    released under this License and any conditions added under section
    7.  This requirement modifies the requirement in section 4 to
    "keep intact all notices".

    c) You must license the entire work, as a whole, under this
    License to anyone who comes into possession of a copy.  This
    License will therefore apply, along with any applicable section 7
    additional terms, to the whole of the work, and all its parts,
    regardless of how they are packaged.  This License gives no
    permission to license the work in any other way, but it does not
    invalidate such permission if you have separately received it.

    d) If the work has interactive user interfaces, each must display
    Appropriate Legal Notices; however, if the Program has interactive
    interfaces that do not display Appropriate Legal Notices, your
    work need not make them do so.

  A compilation of a covered work with other separate and independent
works, which are not by their nature extensions of the covered work,
and which are not combined with it such as to form a larger program,
in or on a volume of a storage or distribution medium, is called an
"aggregate" if the compilation and its resulting copyright are not
used to limit the access or legal rights of the compilation's users
beyond what the individual works permit.  Inclusion of a covered work
in an aggregate does not cause this License to apply to the other
parts of the aggregate.

  6. Conveying Non-Source Forms.

  You may convey a covered work in object code form under the terms
of sections 4 and 5, provided that you also convey the
machine-readable Corresponding Source under the terms of this License,
in one of these ways:

    a) Convey the object code in, or embodied in, a physical product
    (including a physical distribution medium), accompanied by the
    Corresponding Source fixed on a durable physical medium
    customarily used for software interchange.

    b) Convey the object code in, or embodied in, a physical product
    (including a physical distribution medium), accompanied by a
    written offer, valid for at least three years and valid for as
    long as you offer spare parts or customer support for that product
    model, to give anyone who possesses the object code either (1) a
    copy of the Corresponding Source for all the software in the
    product that is covered by this License, on a durable physical
    medium customarily used for software interchange, for a price no
    more than your reasonable cost of physically performing this
    conveying of source, or (2) access to copy the
    Corresponding Source from a network server at no charge.

    c) Convey individual copies of the object code with a copy of the
    written offer to provide the Corresponding Source.  This
    alternative is allowed only occasionally and noncommercially, and
    only if you received the object code with such an offer, in accord
    with subsection 6b.

    d) Convey the object code by offering access from a designated
    place (gratis or for a charge), and offer equivalent access to the
    Corresponding Source in the same way through the same place at no
    further charge.  You need not require recipients to copy the
    Corresponding Source along with the object code.  If the place to
    copy the object code is a network server, the Corresponding Source
    may be on a different server (operated by you or a third party)
    that supports equivalent copying facilities, provided you maintain
    clear directions next to the object code saying where to find the
    Corresponding Source.  Regardless of what server hosts the
    Corresponding Source, you remain obligated to ensure that it is
    available for as long as needed to satisfy these requirements.

    e) Convey the object code using peer-to-peer transmission, provided
    you inform other peers where the object code and Corresponding
    Source of the work are being offered to the general public at no
    charge under subsection 6d.

  A separable portion of the object code, whose source code is excluded
from the Corresponding Source as a System Library, need not be
included in conveying the object code work.

  A "User Product" is either (1) a "consumer product", which means any
tangible personal property which is normally used for personal, family,
or household purposes, or (2) anything designed or sold for incorporation
into a dwelling.  In determining whether a product is a consumer product,
doubtful cases shall be resolved in favor of coverage.  For a particular
product received by a particular user, "normally used" refers to a
typical or common use of that class of product, regardless of the status
of the particular user or of the way in which the particular user
actually uses, or expects or is expected to use, the product.  A product
is a consumer product regardless of whether the product has substantial
commercial, industrial or non-consumer uses, unless such uses represent
the only significant mode of use of the product.

  "Installation Information" for a User Product means any methods,
procedures, authorization keys, or other information required to install
and execute modified versions of a covered work in that User Product from
a modified version of its Corresponding Source.  The information must
suffice to ensure that the continued functioning of the modified object
code is in no case prevented or interfered with solely because
modification has been made.

  If you convey an object code work under this section in, or with, or
specifically for use in, a User Product, and the conveying occurs as
part of a transaction in which the right of possession and use of the
User Product is transferred to the recipient in perpetuity or for a
fixed term (regardless of how the transaction is characterized), the
Corresponding Source conveyed under this section must be accompanied
by the Installation Information.  But this requirement does not apply
if neither you nor any third party retains the ability to install
modified object code on the User Product (for example, the work has
been installed in ROM).

  The requirement to provide Installation Information does not include a
requirement to continue to provide support service, warranty, or updates
for a work that has been modified or installed by the recipient, or for
the User Product in which it has been modified or installed.  Access to a
network may be denied when the modification itself materially and
adversely affects the operation of the network or violates the rules and
protocols for communication across the network.

  Corresponding Source conveyed, and Installation Information provided,
in accord with this section must be in a format that is publicly
documented (and with an implementation available to the public in
source code form), and must require no special password or key for
unpacking, reading or copying.

  7. Additional Terms.

  "Additional permissions" are terms that supplement the terms of this
License by making exceptions from one or more of its conditions.
Additional permissions that are applicable to the entire Program shall
be treated as though they were included in this License, to the extent
that they are valid under applicable law.  If additional permissions
apply only to part of the Program, that part may be used separately
under those permissions, but the entire Program remains governed by
this License without regard to the additional permissions.

  When you convey a copy of a covered work, you may at your option
remove any additional permissions from that copy, or from any part of
it.  (Additional permissions may be written to require their own
removal in certain cases when you modify the work.)  You may place
additional permissions on material, added by you to a covered work,
for which you have or can give appropriate copyright permission.

  Notwithstanding any other provision of this License, for material you
add to a covered work, you may (if authorized by the copyright holders of
that material) supplement the terms of this License with terms:

    a) Disclaiming warranty or limiting liability differently from the
    terms of sections 15 and 16 of this License; or

    b) Requiring preservation of specified reasonable legal notices or
    author attributions in that material or in the Appropriate Legal
    Notices displayed by works containing it; or

    c) Prohibiting misrepresentation of the origin of that material, or
    requiring that modified versions of such material be marked in
    reasonable ways as different from the original version; or

    d) Limiting the use for publicity purposes of names of licensors or
    authors of the material; or

    e) Declining to grant rights under trademark law for use of some
    trade names, trademarks, or service marks; or

    f) Requiring indemnification of licensors and authors of that
    material by anyone who conveys the material (or modified versions of
    it) with contractual assumptions of liability to the recipient, for
    any liability that these contractual assumptions directly impose on
    those licensors and authors.

  All other non-permissive additional terms are considered "further
restrictions" within the meaning of section 10.  If the Program as you
received it, or any part of it, contains a notice stating that it is
governed by this License along with a term that is a further
restriction, you may remove that term.  If a license document contains
a further restriction but permits relicensing or conveying under this
License, you may add to a covered work material governed by the terms
of that license document, provided that the further restriction does
not survive such relicensing or conveying.

  If you add terms to a covered work in accord with this section, you
must place, in the relevant source files, a statement of the
additional terms that apply to those files, or a notice indicating
where to find the applicable terms.

  Additional terms, permissive or non-permissive, may be stated in the
form of a separately written license, or stated as exceptions;
the above requirements apply either way.

  8. Termination.

  You may not propagate or modify a covered work except as expressly
provided under this License.  Any attempt otherwise to propagate or
modify it is void, and will automatically terminate your rights under
this License (including any patent licenses granted under the third
paragraph of section 11).

  However, if you cease all violation of this License, then your
license from a particular copyright holder is reinstated (a)
provisionally, unless and until the copyright holder explicitly and
finally terminates your license, and (b) permanently, if the copyright
holder fails to notify you of the violation by some reasonable means
prior to 60 days after the cessation.

  Moreover, your license from a particular copyright holder is
reinstated permanently if the copyright holder notifies you of the
violation by some reasonable means, this is the first time you have
received notice of violation of this License (for any work) from that
copyright holder, and you cure the violation prior to 30 days after
your receipt of the notice.

  Termination of your rights under this section does not terminate the
licenses of parties who have received copies or rights from you under
this License.  If your rights have been terminated and not permanently
reinstated, you do not qualify to receive new licenses for the same
material under section 10.

  9. Acceptance Not Required for Having Copies.

  You are not required to accept this License in order to receive or
run a copy of the Program.  Ancillary propagation of a covered work
occurring solely as a consequence of using peer-to-peer transmission
to receive a copy likewise does not require acceptance.  However,
nothing other than this License grants you permission to propagate or
modify any covered work.  These actions infringe copyright if you do
not accept this License.  Therefore, by modifying or propagating a
covered work, you indicate your acceptance of this License to do so.

  10. Automatic Licensing of Downstream Recipients.

  Each time you convey a covered work, the recipient automatically
receives a license from the original licensors, to run, modify and
propagate that work, subject to this License.  You are not responsible
for enforcing compliance by third parties with this License.

  An "entity transaction" is a transaction transferring control of an
organization, or substantially all assets of one, or subdividing an
organization, or merging organizations.  If propagation of a covered
work results from an entity transaction, each party to that
transaction who receives a copy of the work also receives whatever
licenses to the work the party's predecessor in interest had or could
give under the previous paragraph, plus a right to possession of the
Corresponding Source of the work from the predecessor in interest, if
the predecessor has it or can get it with reasonable efforts.

  You may not impose any further restrictions on the exercise of the
rights granted or affirmed under this License.  For example, you may
not impose a license fee, royalty, or other charge for exercise of
rights granted under this License, and you may not initiate litigation
(including a cross-claim or counterclaim in a lawsuit) alleging that
any patent claim is infringed by making, using, selling, offering for
sale, or importing the Program or any portion of it.

  11. Patents.

  A "contributor" is a copyright holder who authorizes use under this
License of the Program or a work on which the Program is based.  The
work thus licensed is called the contributor's "contributor version".

  A contributor's "essential patent claims" are all patent claims
owned or controlled by the contributor, whether already acquired or
hereafter acquired, that would be infringed by some manner, permitted
by this License, of making, using, or selling its contributor version,
but do not include claims that would be infringed only as a
consequence of further modification of the contributor version.  For
purposes of this definition, "control" includes the right to grant
patent sublicenses in a manner consistent with the requirements of
this License.

  Each contributor grants you a non-exclusive, worldwide, royalty-free
patent license under the contributor's essential patent claims, to
make, use, sell, offer for sale, import and otherwise run, modify and
propagate the contents of its contributor version.

  In the following three paragraphs, a "patent license" is any express
agreement or commitment, however denominated, not to enforce a patent
(such as an express permission to practice a patent or covenant not to
sue for patent infringement).  To "grant" such a patent license to a
party means to make such an agreement or commitment not to enforce a
patent against the party.

  If you convey a covered work, knowingly relying on a patent license,
and the Corresponding Source of the work is not available for anyone
to copy, free of charge and under the terms of this License, through a
publicly available network server or other readily accessible means,
then you must either (1) cause the Corresponding Source to be so
available, or (2) arrange to deprive yourself of the benefit of the
patent license for this particular work, or (3) arrange, in a manner
consistent with the requirements of this License, to extend the patent
license to downstream recipients.  "Knowingly relying" means you have
actual knowledge that, but for the patent license, your conveying the
covered work in a country, or your recipient's use of the covered work
in a country, would infringe one or more identifiable patents in that
country that you have reason to believe are valid.

  If, pursuant to or in connection with a single transaction or
arrangement, you convey, or propagate by procuring conveyance of, a
covered work, and grant a patent license to some of the parties
receiving the covered work authorizing them to use, propagate, modify
or convey a specific copy of the covered work, then the patent license
you grant is automatically extended to all recipients of the covered
work and works based on it.

  A patent license is "discriminatory" if it does not include within
the scope of its coverage, prohibits the exercise of, or is
conditioned on the non-exercise of one or more of the rights that are
specifically granted under this License.  You may not convey a covered
work if you are a party to an arrangement with a third party that is
in the business of distributing software, under which you make payment
to the third party based on the extent of your activity of conveying
the work, and under which the third party grants, to any of the
parties who would receive the covered work from you, a discriminatory
patent license (a) in connection with copies of the covered work
conveyed by you (or copies made from those copies), or (b) primarily
for and in connection with specific products or compilations that
contain the covered work, unless you entered into that arrangement,
or that patent license was granted, prior to 28 March 2007.

  Nothing in this License shall be construed as excluding or limiting
any implied license or other defenses to infringement that may
otherwise be available to you under applicable patent law.

  12. No Surrender of Others' Freedom.

  If conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License.  If you cannot convey a
covered work so as to satisfy simultaneously your obligations under this
License and any other pertinent obligations, then as a consequence you may
not convey it at all.  For example, if you agree to terms that obligate you
to collect a royalty for further conveying from those to whom you convey
the Program, the only way you could satisfy both those terms and this
License would be to refrain entirely from conveying the Program.

  13. Use with the GNU Affero General Public License.

  Notwithstanding any other provision of this License, you have
permission to link or combine any covered work with a work licensed
under version 3 of the GNU Affero General Public License into a single
combined work, and to convey the resulting work.  The terms of this
License will continue to apply to the part which is the covered work,
but the special requirements of the GNU Affero General Public License,
section 13, concerning interaction through a network will apply to the
combination as such.

  14. Revised Versions of this License.

  The Free Software Foundation may publish revised and/or new versions of
the GNU General Public License from time to time.  Such new versions will
be similar in spirit to the present version, but may differ in detail to
address new problems or concerns.

  Each version is given a distinguishing version number.  If the
Program specifies that a certain numbered version of the GNU General
Public License "or any later version" applies to it, you have the
option of following the terms and conditions either of that numbered
version or of any later version published by the Free Software
Foundation.  If the Program does not specify a version number of the
GNU General Public License, you may choose any version ever published
by the Free Software Foundation.

  If the Program specifies that a proxy can decide which future
versions of the GNU General Public License can be used, that proxy's
public statement of acceptance of a version permanently authorizes you
to choose that version for the Program.

  Later license versions may give you additional or different
permissions.  However, no additional obligations are imposed on any
author or copyright holder as a result of your choosing to follow a
later version.

  15. Disclaimer of Warranty.

  THERE IS NO WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY
APPLICABLE LAW.  EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT
HOLDERS AND/OR OTHER PARTIES PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY
OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE PROGRAM
IS WITH YOU.  SHOULD THE PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF
ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

  16. Limitation of Liability.

  IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING
WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MODIFIES AND/OR CONVEYS
THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY
GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE
USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS OF
DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD
PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER PROGRAMS),
EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF
SUCH DAMAGES.

  17. Interpretation of Sections 15 and 16.

  If the disclaimer of warranty and limitation of liability provided
above cannot be given local legal effect according to their terms,
reviewing courts shall apply local law that most closely approximates
an absolute waiver of all civil liability in connection with the
Program, unless a warranty or assumption of liability accompanies a
copy of the Program in return for a fee.

                     END OF TERMS AND CONDITIONS

            How to Apply These Terms to Your New Programs

  If you develop a new program, and you want it to be of the greatest
possible use to the public, the best way to achieve this is to make it
free software which everyone can redistribute and change under these terms.

  To do so, attach the following notices to the program.  It is safest
to attach them to the start of each source file to most effectively
state the exclusion of warranty; and each file should have at least
the "copyright" line and a pointer to where the full notice is found.

    <one line to give the program's name and a brief idea of what it does.>
    Copyright (C) <year>  <name of author>

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

Also add information on how to contact you by electronic and paper mail.

  If the program does terminal interaction, make it output a short
notice like this when it starts in an interactive mode:

    <program>  Copyright (C) <year>  <name of author>
    This program comes with ABSOLUTELY NO WARRANTY; for details type `show w'.
    This is free software, and you are welcome to redistribute it
    under certain conditions; type `show c' for details.

The hypothetical commands `show w' and `show c' should show the appropriate
parts of the General Public License.  Of course, your program's commands
might be different; for a GUI interface, you would use an "about box".

  You should also get your employer (if you work as a programmer) or school,
if any, to sign a "copyright disclaimer" for the program, if necessary.
For more information on this, and how to apply and follow the GNU GPL, see
<https://www.gnu.org/licenses/>.

  The GNU General Public License does not permit incorporating your program
into proprietary programs.  If your program is a subroutine library, you
may consider it more useful to permit linking proprietary applications with
the library.  If this is what you want to do, use the GNU Lesser General
Public License instead of this License.  But first, please read
<https://www.gnu.org/licenses/why-not-lgpl.html>.
</file>

<file path="nest-cli.json">
{
  "$schema": "https://json.schemastore.org/nest-cli",
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "deleteOutDir": true
  }
}
</file>

<file path="src/app.service.ts">
import { Injectable } from '@nestjs/common';
@Injectable()
export class AppService {
  getHello(): string {
    return 'Hello World!';
  }
}
</file>

<file path="src/constant/magic.constant.ts">
export default class MagicConstant {
  static readonly ONE_DAY_SECONDS = 86400;
  static readonly ONE_DAY_MILLISECONDS = 86400000;
  static readonly SEVEN_DAYS_SECONDS = 604800;
  static readonly TWO_HOURS_SECONDS = 7200;
  static readonly ONE_HOUR_SECONDS = 3600;
  static readonly THIRTY_MINUTES_SECONDS = 1800;
  static readonly FIVE_MINUTES_SECONDS = 300;
  static readonly TWO_MINUTES_SECONDS = 120;
  static readonly ONE_MINUTE = 60;
}
</file>

<file path="src/enum/message-type.enum.ts">
export enum MessageType {
  ACTIVITY_ANNOUNCEMENT = 'ACTIVITY_ANNOUNCEMENT',
  ACHIEVEMENT = 'ACHIEVEMENT',
  SYSTEM_ANNOUNCEMENT = 'SYSTEM_ANNOUNCEMENT',
}
</file>

<file path="src/routes/multi-group-config/multi-group-config.controller.ts">
import { Controller, Post, Delete, Body, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { MultiGroupConfigService } from './multi-group-config.service';
import { MultiGroupConfig } from '../../schemas/multi-group-config.schema';
@ApiTags('multi-group-config')
@Controller('multi-group-configs')
export class MultiGroupConfigController {
  constructor(private readonly multiGroupConfigService: MultiGroupConfigService) {}
  @Post('bulk-upsert')
  @ApiOperation({ summary: '批量新增或更新群组配置' })
  async bulkUpsert(@Body() configs: Partial<MultiGroupConfig>[]) {
    return this.multiGroupConfigService.bulkUpsert(configs);
  }
  @Delete('bulk-delete')
  @ApiOperation({ summary: '批量删除群组配置' })
  async bulkDelete(@Body() configs: Array<{ telegramGroupId: string; gameChatChannelId: number }>) {
    const deletedCount = await this.multiGroupConfigService.bulkDelete(configs);
    return { deletedCount };
  }
  @Get()
  @ApiOperation({ summary: '获取所有群组配置' })
  async findAll() {
    return this.multiGroupConfigService.findAll();
  }
}
</file>

<file path="src/routes/multi-group-config/multi-group-config.module.ts">
import { Module } from '@nestjs/common';
import { MultiGroupConfigController } from './multi-group-config.controller';
import { MultiGroupConfigService } from './multi-group-config.service';
import { MongooseModule } from '@nestjs/mongoose';
import { MultiGroupConfig, MultiGroupConfigSchema } from '../../schemas/multi-group-config.schema';
@Module({
  imports: [MongooseModule.forFeature([{ name: MultiGroupConfig.name, schema: MultiGroupConfigSchema }])],
  controllers: [MultiGroupConfigController],
  providers: [MultiGroupConfigService],
  exports: [MultiGroupConfigService],
})
export class MultiGroupConfigModule {}
</file>

<file path="src/routes/multi-group-config/multi-group-config.service.ts">
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MultiGroupConfig, MultiGroupConfigDocument } from '../../schemas/multi-group-config.schema';
import { BasicUtils } from '../../utils/basic.utils';
@Injectable()
export class MultiGroupConfigService {
  constructor(
    @InjectModel(MultiGroupConfig.name)
    private multiGroupConfigModel: Model<MultiGroupConfigDocument>,
  ) {}
  async bulkUpsert(configs: Partial<MultiGroupConfig>[]) {
    const operations = configs.map((config) => ({
      updateOne: {
        filter: {
          telegramGroupId: config.telegramGroupId,
          gameChatChannelId: config.gameChatChannelId,
        },
        update: { $set: config },
        upsert: true,
      },
    }));
    await this.multiGroupConfigModel.bulkWrite(operations);
    const res = await this.multiGroupConfigModel
      .find({
        $or: configs.map((config) => ({
          telegramGroupId: config.telegramGroupId,
          gameChatChannelId: config.gameChatChannelId,
        })),
      })
      .exec();
    return BasicUtils.okResponse(res);
  }
  async bulkDelete(configs: Array<{ telegramGroupId: string; gameChatChannelId: number }>) {
    const result = await this.multiGroupConfigModel.deleteMany({
      $or: configs.map((config) => ({
        telegramGroupId: config.telegramGroupId,
        gameChatChannelId: config.gameChatChannelId,
      })),
    });
    return result.deletedCount;
  }
  async findAll() {
    const res = await this.multiGroupConfigModel.find().exec();
    return BasicUtils.okResponse(res);
  }
}
</file>

<file path="src/schemas/ad-filter-config.schema.ts">
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
@Schema({ collection: 'ad_filter_configs', timestamps: true })
export class AdFilterConfig extends Document {
  @Prop({ required: true, default: true })
  enabled: boolean;
  @Prop({ type: [String], required: true, default: [] })
  keywords: string[];
  @Prop({ type: [String], required: true, default: [] })
  urlPatterns: string[];
  @Prop({ type: [String], required: true, default: [] })
  contactPatterns: string[];
  @Prop({ required: true, default: 0.3 })
  adThreshold: number;
}
export type AdFilterConfigDocument = AdFilterConfig & Document;
export const AdFilterConfigSchema = SchemaFactory.createForClass(AdFilterConfig);
</file>

<file path="src/schemas/data-push-config.schema.ts">
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
export interface TargetChat {
  chatId: string;
  threadId?: number;
  enabled: boolean;
  description?: string;
}
@Schema({
  collection: 'data_push_configs',
  timestamps: true,
  versionKey: false,
})
export class DataPushConfig {
  @Prop({ required: true, unique: true, default: 'default' })
  configId: string;
  @Prop({ required: true, default: false })
  enabled: boolean;
  @Prop({ type: [Object], default: [] })
  targetChats: TargetChat[];
  @Prop({ required: true, default: 3 })
  maxRetries: number;
  @Prop({ required: true, default: 5000 })
  retryInterval: number;
  @Prop({ required: true, default: 30000 })
  timeout: number;
}
export type DataPushConfigDocument = DataPushConfig & Document;
export const DataPushConfigSchema = SchemaFactory.createForClass(DataPushConfig);
DataPushConfigSchema.index({ configId: 1 }, { unique: true });
DataPushConfigSchema.index({ enabled: 1 });
DataPushConfigSchema.index({ createdAt: 1 });
</file>

<file path="src/schemas/message-status.schema.ts">
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
@Schema({ collection: 'message_status', timestamps: true })
export class MessageStatusDocument extends Document {
  @Prop({ required: true, index: true })
  messageId!: string;
  @Prop({ required: true, default: 'processed' })
  status!: string;
  @Prop({ required: true, default: Date.now })
  processedAt!: Date;
  @Prop({ required: false })
  source?: string;
  @Prop({ required: false })
  target?: string;
  @Prop({ required: false })
  chatId?: string;
  @Prop({ required: false })
  threadId?: string;
  @Prop({ required: false })
  fromUser?: string;
  @Prop({ required: false })
  content?: string;
  @Prop({ required: false })
  errorMessage?: string;
  @Prop({ required: false })
  timestamp?: number;
  @Prop({
    type: {
      retryCount: { type: Number, default: 0 },
      lastRetryAt: { type: Date },
      maxRetries: { type: Number, default: 3 },
    },
    default: {
      retryCount: 0,
      maxRetries: 3,
    },
  })
  retryInfo!: {
    retryCount: number;
    lastRetryAt?: Date;
    maxRetries: number;
  };
  @Prop({
    type: {
      processingTime: { type: Number },
      queueTime: { type: Number },
      totalTime: { type: Number },
    },
    default: {},
  })
  performance?: {
    processingTime?: number;
    queueTime?: number;
    totalTime?: number;
  };
  @Prop({
    type: {
      version: String,
      description: String,
      tags: [String],
    },
    default: {
      version: '2.0.0',
      description: '消息处理状态记录 - 重构版本',
      tags: ['messaging', 'status'],
    },
  })
  metadata!: {
    version: string;
    description: string;
    tags?: string[];
  };
}
export const MessageStatusSchema = SchemaFactory.createForClass(MessageStatusDocument);
MessageStatusSchema.index({ processedAt: 1 });
MessageStatusSchema.index({ status: 1 });
MessageStatusSchema.index({ source: 1 });
MessageStatusSchema.index({ chatId: 1 });
MessageStatusSchema.index({ timestamp: 1 });
MessageStatusSchema.index({ 'retryInfo.retryCount': 1 });
</file>

<file path="src/schemas/system-config.schema.ts">
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
export interface DataPushSystemConfig {
  enabled: boolean;
  apiUrl?: string;
  apiKey?: string;
  interval?: number;
  batchSize?: number;
  targetChats?: {
    chatId: string;
    threadId?: string;
    enabled: boolean;
    description?: string;
  }[];
  autoStart?: boolean;
  timeout?: number;
  retryConfig?: {
    maxAttempts: number;
    delay: number;
  };
}
export interface GroupMappingSystemConfig {
  enabled: boolean;
  defaultMapping?: {
    telegramGroupId: string;
    gameChatChannelId: number;
    telegramThreadId?: string;
    description?: string;
  }[];
  autoCreateMapping?: boolean;
  validationRules?: {
    requireDescription: boolean;
    allowDuplicateChannels: boolean;
  };
}
@Schema({ collection: 'system_configs', timestamps: true })
export class SystemConfig extends Document {
  @Prop({ required: true, unique: true })
  configKey!: string;
  @Prop({ required: true })
  configName!: string;
  @Prop()
  description?: string;
  @Prop({ type: Object })
  dataPush?: DataPushSystemConfig;
  @Prop({ type: Object })
  groupMapping?: GroupMappingSystemConfig;
  @Prop({ default: true })
  enabled!: boolean;
  @Prop({ default: 1 })
  version!: number;
  @Prop()
  lastModifiedBy?: string;
  @Prop()
  notes?: string;
}
export const SystemConfigSchema = SchemaFactory.createForClass(SystemConfig);
export type SystemConfigDocument = SystemConfig & Document;
</file>

<file path="tsconfig.build.json">
{
  "extends": "./tsconfig.json",
  "exclude": ["node_modules", "test", "dist", "**/*spec.ts"]
}
</file>

<file path="tsconfig.json">
{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2023",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "noFallthroughCasesInSwitch": false
  }
}
</file>

<file path=".prettierrc">
{
  "semi": true,
  "singleQuote": true,
  "trailingComma": "all",
  "printWidth": 120,
  "tabWidth": 2,
  "endOfLine": "auto"
}
</file>

<file path="src/constant/http.constant.ts">
export const httpCode = {
  UNISAT_OK: {
    code: 0,
    msg: 'ok',
  },
  OK: {
    code: 1,
    msg: 'ok',
  },
  FAILED: {
    code: 2,
    msg: 'failed',
  },
  UNKNOWN_ERROR: {
    code: 3,
    msg: 'unknown error',
  },
  TELEGRAM_API_ERROR: {
    code: 100,
    msg: 'Telegram API调用失败',
  },
  TELEGRAM_RATE_LIMIT: {
    code: 101,
    msg: 'Telegram API频率限制',
  },
  TELEGRAM_PERMISSION_ERROR: {
    code: 102,
    msg: 'Telegram Bot权限不足',
  },
  TELEGRAM_NETWORK_ERROR: {
    code: 103,
    msg: 'Telegram网络连接错误',
  },
  MESSAGE_VALIDATION_ERROR: {
    code: 200,
    msg: '消息格式验证失败',
  },
  MESSAGE_QUEUE_ERROR: {
    code: 201,
    msg: '消息队列处理失败',
  },
  CONFIG_ERROR: {
    code: 300,
    msg: '配置错误',
  },
};
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  RATE_LIMIT = 'RATE_LIMIT',
  CONFIG = 'CONFIG',
  UNKNOWN = 'UNKNOWN',
}
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}
</file>

<file path="src/constant/mq.constant.ts">
export const queues = {
  GAME_CHAT_SYNC_TG_QUEUE: 'game-chat-sync-tg-queue',
  TG_CHAT_SYNC_GAME_QUEUE: 'tg-chat-sync-game-queue',
};
export const jobs = {
  GAME_CHAT_SYNC_TG_JOB: 'game-chat-sync-tg-job',
  TG_CHAT_SYNC_GAME_JOB: 'tg-chat-sync-game-job',
};
</file>

<file path="src/redis/redis.module.ts">
import { RedisModule as NestRedisModule } from '@nestjs-modules/ioredis';
import { Module } from '@nestjs/common';
import { RedisService } from './redis.service';
import { BullModule } from '@nestjs/bull';
import * as dotenv from 'dotenv';
dotenv.config();
@Module({
  imports: [
    BullModule.forRoot({
      url: process.env.MQ_REDIS_URL!,
      redis: {
        password: process.env.MQ_REDIS_PASSWORD!,
        db: Number(process.env.MQ_REDIS_DATABASE!),
      },
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: 1000,
      },
    }),
    NestRedisModule.forRootAsync({
      useFactory: () =>
        process.env.REDIS_TYPE === 'cluster'
          ? {
              type: 'cluster',
              nodes: process.env.REDIS_NODES!.split(',').map((node) => ({
                host: node,
                port: Number(process.env.REDIS_PORT!),
                password: process.env.REDIS_PASSWORD!,
                db: Number(process.env.REDIS_DATABASE!),
              })),
            }
          : {
              type: 'single',
              url: process.env.REDIS_URL!,
              options: {
                password: process.env.REDIS_PASSWORD!,
                db: Number(process.env.REDIS_DATABASE!),
              },
            },
    }),
  ],
  providers: [RedisService],
  exports: [RedisService],
})
export class RedisModule {}
</file>

<file path="src/routes/telegram/handler/telegram-to-game.handle.ts">
import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { jobs, queues } from '../../../constant/mq.constant';
import { Job } from 'bull';
import { TelegramService } from '../telegram.service';
@Injectable()
@Processor(queues.TG_CHAT_SYNC_GAME_QUEUE)
export class TelegramToGameHandle {
  private readonly logger: Logger = new Logger(TelegramToGameHandle.name);
  constructor(private readonly telegramService: TelegramService) {}
  @Process(jobs.TG_CHAT_SYNC_GAME_JOB)
  async handleTask(job: Job) {
    const { message, telegramGroupId } = job.data;
    await this.telegramService.handleSyncToGame(message, telegramGroupId);
  }
}
</file>

<file path="src/routes/telegram/telegram.module.ts">
import { Module } from '@nestjs/common';
import { TelegramController } from './telegram.controller';
import { TelegramService } from './telegram.service';
import { MessageModule } from '../../message/message.module';
import { BullModule } from '@nestjs/bull';
import { queues } from '../../constant/mq.constant';
import { TelegramToGameHandle } from './handler/telegram-to-game.handle';
import { MongooseModule } from '@nestjs/mongoose';
import { MultiGroupConfig, MultiGroupConfigSchema } from '../../schemas/multi-group-config.schema';
@Module({
  imports: [
    MessageModule,
    BullModule.registerQueue({ name: queues.TG_CHAT_SYNC_GAME_QUEUE }),
    MongooseModule.forFeature([
      {
        name: MultiGroupConfig.name,
        schema: MultiGroupConfigSchema,
      },
    ]),
  ],
  controllers: [TelegramController],
  providers: [TelegramService, TelegramToGameHandle],
  exports: [TelegramService],
})
export class TelegramModule {}
</file>

<file path="src/utils/error.utils.ts">
import { Logger } from '@nestjs/common';
import { httpCode, ErrorType, ErrorSeverity } from '../constant/http.constant';
export interface ErrorInfo {
  type: ErrorType;
  severity: ErrorSeverity;
  code: number;
  message: string;
  details?: any;
  context?: string;
  timestamp?: Date;
  retryable?: boolean;
}
export class ErrorUtils {
  static formatError(error: any, context?: string, additionalInfo?: any): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      code: httpCode.UNKNOWN_ERROR.code,
      message: '未知错误',
      context,
      timestamp: new Date(),
      retryable: false,
    };
    if (this.isNetworkError(error)) {
      errorInfo.type = ErrorType.NETWORK;
      errorInfo.severity = ErrorSeverity.MEDIUM;
      errorInfo.code = httpCode.TELEGRAM_NETWORK_ERROR.code;
      errorInfo.message = this.getNetworkErrorMessage(error);
      errorInfo.retryable = true;
    } else if (this.isTelegramApiError(error)) {
      errorInfo.type = ErrorType.PERMISSION;
      errorInfo.severity = this.getTelegramErrorSeverity(error);
      errorInfo.code = this.getTelegramErrorCode(error);
      errorInfo.message = this.getTelegramErrorMessage(error);
      errorInfo.retryable = this.isTelegramErrorRetryable(error);
    } else if (this.isValidationError(error)) {
      errorInfo.type = ErrorType.VALIDATION;
      errorInfo.severity = ErrorSeverity.LOW;
      errorInfo.code = httpCode.MESSAGE_VALIDATION_ERROR.code;
      errorInfo.message = `数据验证失败: ${error.message || '格式不正确'}`;
      errorInfo.retryable = false;
    } else if (this.isConfigError(error)) {
      errorInfo.type = ErrorType.CONFIG;
      errorInfo.severity = ErrorSeverity.HIGH;
      errorInfo.code = httpCode.CONFIG_ERROR.code;
      errorInfo.message = `配置错误: ${error.message || '配置项缺失或无效'}`;
      errorInfo.retryable = false;
    }
    errorInfo.details = {
      originalError: error.message || String(error),
      stack: error.stack,
      ...additionalInfo,
    };
    return errorInfo;
  }
  static logError(logger: Logger, errorInfo: ErrorInfo, operationId?: string) {
    const logPrefix = operationId ? `[${operationId}]` : '';
    const severityIcon = this.getSeverityIcon(errorInfo.severity);
    const retryableText = errorInfo.retryable ? '可重试' : '不可重试';
    const logMessage = `${logPrefix} ${severityIcon} [${errorInfo.type}] ${errorInfo.message} (${retryableText})`;
    switch (errorInfo.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error(logMessage);
        if (errorInfo.details) {
          logger.error(`错误详情: ${JSON.stringify(errorInfo.details, null, 2)}`);
        }
        break;
      case ErrorSeverity.HIGH:
        logger.error(logMessage);
        if (errorInfo.details?.originalError) {
          logger.error(`原始错误: ${errorInfo.details.originalError}`);
        }
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn(logMessage);
        break;
      case ErrorSeverity.LOW:
        logger.log(logMessage);
        break;
    }
    // 如果有上下文信息，单独记录
    if (errorInfo.context) {
      logger.debug(`错误上下文: ${errorInfo.context}`);
    }
  }
  /**
   * 判断是否为网络错误
   */
  private static isNetworkError(error: any): boolean {
    const networkCodes = ['ECONNABORTED', 'ENOTFOUND', 'ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED'];
    return networkCodes.includes(error.code) || error.message?.includes('timeout');
  }
  private static isTelegramApiError(error: any): boolean {
    return error.response?.data?.error_code || error.response?.status;
  }
  private static isValidationError(error: any): boolean {
    return (
      error.message?.includes('验证') ||
      error.message?.includes('格式') ||
      error.message?.includes('缺失') ||
      error.name === 'ValidationError'
    );
  }
  private static isConfigError(error: any): boolean {
    return (
      error.message?.includes('配置') ||
      error.message?.includes('环境变量') ||
      error.message?.includes('GAME_CHAT_CHANNEL_ID')
    );
  }
  private static getNetworkErrorMessage(error: any): string {
    const errorMap: Record<string, string> = {
      ECONNABORTED: '连接超时，请检查网络连接',
      ENOTFOUND: 'DNS解析失败，请检查网络设置',
      ECONNRESET: '连接被重置，请稍后重试',
      ETIMEDOUT: '请求超时，请检查网络状况',
      ECONNREFUSED: '连接被拒绝，请检查服务状态',
    };
    return errorMap[error.code] || `网络错误: ${error.message || '未知网络问题'}`;
  }
  private static getTelegramErrorSeverity(error: any): ErrorSeverity {
    const status = error.response?.status;
    if (status === 403) return ErrorSeverity.HIGH;
    if (status === 429) return ErrorSeverity.MEDIUM;
    if (status >= 500) return ErrorSeverity.HIGH;
    return ErrorSeverity.MEDIUM;
  }
  private static getTelegramErrorCode(error: any): number {
    const status = error.response?.status;
    if (status === 403) return httpCode.TELEGRAM_PERMISSION_ERROR.code;
    if (status === 429) return httpCode.TELEGRAM_RATE_LIMIT.code;
    return httpCode.TELEGRAM_API_ERROR.code;
  }
  private static getTelegramErrorMessage(error: any): string {
    const status = error.response?.status;
    const description = error.response?.data?.description;
    const errorCode = error.response?.data?.error_code;
    let message = 'Telegram API调用失败';
    if (status === 403) {
      message = 'Bot权限不足，请检查Bot是否已加入群组并有发送消息权限';
    } else if (status === 429) {
      const retryAfter = error.response?.data?.parameters?.retry_after;
      message = `触发频率限制${retryAfter ? `，请等待${retryAfter}秒后重试` : ''}`;
    } else if (status === 400) {
      message = `请求参数错误: ${description || '参数格式不正确'}`;
    } else if (status >= 500) {
      message = `Telegram服务器错误 (${status}): ${description || '服务暂时不可用'}`;
    }
    if (errorCode) {
      message += ` (错误码: ${errorCode})`;
    }
    return message;
  }
  private static isTelegramErrorRetryable(error: any): boolean {
    const status = error.response?.status;
    return status === 429 || status >= 500;
  }
  private static getSeverityIcon(severity: ErrorSeverity): string {
    const icons: Record<ErrorSeverity, string> = {
      [ErrorSeverity.LOW]: '🔵',
      [ErrorSeverity.MEDIUM]: '🟡',
      [ErrorSeverity.HIGH]: '🟠',
      [ErrorSeverity.CRITICAL]: '🔴',
    };
    return icons[severity];
  }
  static createErrorResponse(errorInfo: ErrorInfo) {
    return {
      code: errorInfo.code,
      msg: errorInfo.message,
      type: errorInfo.type,
      retryable: errorInfo.retryable,
      timestamp: errorInfo.timestamp,
    };
  }
  static getRetryDelay(error: any): number {
    if (error.response?.status === 429) {
      const retryAfter = error.response?.data?.parameters?.retry_after;
      return retryAfter ? retryAfter * 1000 : 60000;
    }
    if (this.isNetworkError(error)) {
      return Math.min(30000, 1000 * Math.pow(2, Math.random() * 3));
    }
    return 5000;
  }
}
</file>

<file path=".env.test">
# Telegram 配置
TELEGRAM_BOT_TOKEN=**********************************************

# 1001 - 主聊天窗口, 1002 - 系统消息窗口, 1 - 默认窗口
# 为了兼容性保留此配置，但在多群组模式下不会使用
GAME_CHAT_CHANNEL_ID=1001

# 多群组配置（可选）
# 启用多群组模式时，可以配置多个Telegram群组对应不同的游戏聊天频道
# 格式：JSON数组，每个对象包含telegramGroupId、gameChatChannelId和可选的telegramThreadId
MULTI_GROUP_MAPPINGS=[{"telegramGroupId":"@grassPush","gameChatChannelId":1001,"telegramThreadId":"1"},{"telegramGroupId":"@testFractalPush","gameChatChannelId":1002}]


# 游戏聊天 API 配置
GAME_CHAT_API_URL=http://135.181.78.188:8080
# 游戏聊天频道ID，用于指定消息发送到哪个游戏窗口


# 管理员配置（逗号分隔的用户名列表，支持带@或不带@的格式）
TELEGRAM_ADMIN_USERNAMES=@alon0918

# 服务配置
PORT=3001
NODE_ENV=development

# Telegram Bot 配置
FORCE_REAL_MODE=true
DISABLE_TEST_MODE=true

# 代理配置（可选）
HTTP_PROXY=http://127.0.0.1:7897
HTTPS_PROXY=http://127.0.0.1:7897
SOCKS_PROXY=socks5://127.0.0.1:7897

# Redis配置
#REDIS_TYPE=cluster
#REDIS_NODES=
#REDIS_PORT=
#REDIS_PASSWORD=
#REDIS_DATABASE=

REDIS_TYPE=single
REDIS_URL=redis://121.91.175.154:6379
REDIS_PASSWORD=3a6f0e74f57F@b91
REDIS_DATABASE=10

# Redis MQ配置
MQ_REDIS_URL=redis://121.91.175.154:6379
MQ_REDIS_PASSWORD=3a6f0e74f57F@b91
MQ_REDIS_DATABASE=11

BOT_INSTANCE_ID=unique_instance_id
MAX_BOT_RETRIES=3
BOT_RESTART_DELAY=10000
</file>

<file path="config/README.md">
# 广告过滤配置说明

## 概述

本项目支持动态配置广告过滤规则，配置会自动保存到本地文件中，确保项目重启后配置不丢失。

## 配置文件

### 文件位置

- **默认配置文件**: `config/ad-filter-default-config.json` (系统默认配置，请勿修改)
- **用户配置文件**: `config/ad-filter-config.json` (用户自定义配置)
- **示例配置文件**: `config/ad-filter-config.example.json` (配置示例)

### 配置优先级

1. **用户自定义配置** (`ad-filter-config.json`) - 最高优先级
2. **默认配置文件** (`ad-filter-default-config.json`) - 系统默认
3. **内置备用配置** - 当文件加载失败时使用

### 配置结构

```json
{
  "keywords": ["关键词1", "关键词2"],
  "whitelist": ["白名单词1", "白名单词2"],
  "urlPatterns": ["t\\.me", "telegram\\.me"],
  "contactPatterns": ["微信", "QQ", "电话"],
  "thresholds": {
    "repeatedCharThreshold": 5,
    "capsRatio": 0.5,
    "minCapsLength": 10
  },
  "enabled": true,
  "_metadata": {
    "version": "1.0.0",
    "lastUpdated": "2024-01-01T00:00:00.000Z",
    "description": "配置文件描述"
  }
}
```

### 配置项说明

| 配置项                             | 类型     | 说明                             |
| ---------------------------------- | -------- | -------------------------------- |
| `keywords`                         | string[] | 广告关键词黑名单                 |
| `whitelist`                        | string[] | 白名单关键词（优先级高于黑名单） |
| `urlPatterns`                      | string[] | URL 正则表达式模式               |
| `contactPatterns`                  | string[] | 联系方式关键词                   |
| `thresholds.repeatedCharThreshold` | number   | 重复字符阈值                     |
| `thresholds.capsRatio`             | number   | 大写字母比例阈值                 |
| `thresholds.minCapsLength`         | number   | 大写检测最小长度                 |
| `enabled`                          | boolean  | 是否启用广告过滤                 |
| `_metadata`                        | object   | 元数据信息（自动生成）           |

## API 接口

### 添加黑名单关键词

```http
POST /admin/ad-keywords
Content-Type: application/json

{
  "keywords": ["新关键词1", "新关键词2"]
}
```

### 移除黑名单关键词

```http
DELETE /admin/ad-keywords
Content-Type: application/json

{
  "keywords": ["要删除的关键词"]
}
```

### 添加白名单关键词

```http
POST /admin/whitelist-keywords
Content-Type: application/json

{
  "keywords": ["白名单关键词"]
}
```

### 更新配置

```http
PUT /admin/ad-filter-config
Content-Type: application/json

{
  "enabled": true,
  "thresholds": {
    "repeatedCharThreshold": 6
  }
}
```

### 切换过滤开关

```http
POST /admin/toggle-ad-filter
Content-Type: application/json

{
  "enabled": false
}
```

## 特性

### 🔄 自动持久化

- 通过 API 修改的配置会自动保存到本地文件
- 项目重启后配置自动加载
- 支持增量保存，只保存与默认配置不同的部分

### 📊 配置优先级

1. **自定义配置**（代码中传入的配置）
2. **本地文件配置**（`ad-filter-config.json`）
3. **默认配置**（代码中的默认值）

### 🛡️ 向后兼容

- 保持现有 API 接口不变
- 支持渐进式迁移到数据库
- 配置文件格式向前兼容

### 🔍 智能去重

- 添加关键词时自动去重
- 只有实际发生变更时才保存文件
- 优化性能，减少不必要的 I/O 操作

## 日志输出

系统会输出以下日志信息：

- ✅ 成功加载广告过滤配置文件
- 📝 广告过滤配置文件不存在，将使用默认配置
- ❌ 加载/保存配置文件失败
- ✅ 广告过滤配置已保存到文件

## 注意事项

1. **配置文件管理**:

   - `ad-filter-default-config.json`: 系统默认配置，**请勿手动修改**
   - `ad-filter-config.json`: 用户自定义配置，可以手动编辑或通过API修改
   - 系统会自动从默认配置文件加载基础配置，然后应用用户自定义配置

2. **文件权限**: 确保应用有读写配置文件的权限

3. **备份**: 建议定期备份用户配置文件 `ad-filter-config.json`

4. **格式**: 配置文件必须是有效的 JSON 格式

5. **编码**: 文件编码必须是 UTF-8

6. **正则表达式**: 在JSON文件中，正则表达式以字符串形式存储，系统会自动转换为RegExp对象

7. **迁移**: 如果需要更强大的配置管理，可以考虑迁移到数据库存储

## 迁移到数据库

当需要迁移到数据库时，可以：

1. 保持现有 API 接口不变
2. 修改 `AdFilterConfigManager` 的存储后端
3. 提供数据迁移工具，将文件配置导入数据库
4. 支持配置来源切换（文件 ↔ 数据库）
</file>

<file path="eslint.config.mjs">
// @ts-check
import eslint from '@eslint/js';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import globals from 'globals';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  {
    ignores: ['eslint.config.mjs'],
  },
  eslint.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  eslintPluginPrettierRecommended,
  {
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
      sourceType: 'commonjs',
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  {
    rules: {
      '@typescript-eslint/no-floating-promises': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/interface-name-prefix': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      'prettier/prettier': 'error',
    },
  },
);
</file>

<file path="src/app.controller.ts">
import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
@Controller('app')
export class AppController {
  constructor(private readonly appService: AppService) {}
  @Get('hello')
  getHello(): string {
    return this.appService.getHello();
  }
}
</file>

<file path="src/redis/redis.service.ts">
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { Cluster } from 'ioredis';
import MagicConstant from '../constant/magic.constant';
@Injectable()
export class RedisService implements OnModuleDestroy {
  constructor(@InjectRedis() private readonly clusterClient: Cluster) {}
  async onModuleDestroy() {
    await this.clusterClient.quit();
  }
  async set(key: string, value: any, expirationInSeconds?: number) {
    if (expirationInSeconds) {
      await this.clusterClient.set(key, JSON.stringify(value), 'EX', expirationInSeconds);
    } else {
      await this.clusterClient.set(key, JSON.stringify(value));
    }
  }
  async setWithExp(key: string, value: any, expirationInSeconds: number = MagicConstant.ONE_HOUR_SECONDS) {
    await this.clusterClient.set(key, JSON.stringify(value), 'EX', expirationInSeconds);
  }
  async get(key: string) {
    const value = await this.clusterClient.get(key);
    return value ? JSON.parse(value) : null;
  }
  async del(key: string) {
    await this.clusterClient.del(key);
  }
  async mget(keys: string[]): Promise<(string | null)[]> {
    const values = await this.clusterClient.mget(...keys);
    return values.map((value) => (value ? JSON.parse(value) : null));
  }
  pipeline() {
    return this.clusterClient.pipeline();
  }
  async exists(key: string): Promise<boolean> {
    const result = await this.clusterClient.exists(key);
    return result === 1;
  }
  async expire(key: string, seconds: number): Promise<boolean> {
    const result = await this.clusterClient.expire(key, seconds);
    return result === 1;
  }
}
</file>

<file path="src/routes/game/game.controller.ts">
import { Controller, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SyncDto } from '../../dto/sync.dto';
import { GameService } from './game.service';
@ApiTags('game')
@Controller('game')
export class GameController {
  constructor(private readonly gameService: GameService) {}
  @Post('sync-to-telegram')
  @ApiOperation({
    summary: '推送游戏消息到 Telegram',
    description: `
      将游戏中的消息同步推送到对应的 Telegram 群组主题帖中。
      ## 功能特性
      - 支持普通消息和回复消息的同步
      - 自动构建和维护回复链信息
      - 支持多层回复深度追踪
      - 自动映射游戏频道到 Telegram 群组
      ## 回复链功能
      - replyDepth: 回复深度（0为原始消息）
      - replyChainRootId: 回复链根消息ID
      - replyChainPath: 完整回复路径
    `,
  })
  @ApiResponse({ status: 201, description: '消息同步成功，已推送到目标 Telegram 群组' })
  @ApiResponse({ status: 400, description: '请求参数错误，请检查消息格式和必填字段' })
  @ApiResponse({ status: 500, description: '服务器内部错误，消息同步失败' })
  async syncToTelegram(@Body() dto: SyncDto) {
    return this.gameService.syncToTelegram(dto);
  }
}
</file>

<file path="src/routes/game/game.module.ts">
import { Module } from '@nestjs/common';
import { GameController } from './game.controller';
import { GameService } from './game.service';
import { MessageModule } from '../../message/message.module';
import { BullModule } from '@nestjs/bull';
import { queues } from '../../constant/mq.constant';
import { GameToTelegramHandle } from './handler/game-to-telegram.handle';
import { MongooseModule } from '@nestjs/mongoose';
import { MultiGroupConfig, MultiGroupConfigSchema } from '../../schemas/multi-group-config.schema';
@Module({
  imports: [
    MessageModule,
    BullModule.registerQueue({
      name: queues.GAME_CHAT_SYNC_TG_QUEUE,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      },
    }),
    MongooseModule.forFeature([
      {
        name: MultiGroupConfig.name,
        schema: MultiGroupConfigSchema,
      },
    ]),
  ],
  controllers: [GameController],
  providers: [GameService, GameToTelegramHandle],
  exports: [GameService],
})
export class GameModule {}
</file>

<file path="src/schemas/multi-group-config.schema.ts">
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
@Schema({ collection: 'multi_group_configs', timestamps: true })
export class MultiGroupConfig extends Document {
  @Prop({ required: true, unique: true })
  telegramGroupId: string;
  @Prop({ required: true, unique: true })
  gameChatChannelId: number;
  @Prop()
  telegramThreadId?: string;
  @Prop({ type: [String] })
  telegramListenThreadIds?: string[];
  @Prop({ required: true, default: true })
  enabled: boolean;
  @Prop({ required: true })
  priority: number;
  @Prop({ required: true })
  description: string;
  @Prop({
    type: {
      adminUsers: [String],
    },
  })
  adminConfig: {
    adminUsers: string[];
  };
  @Prop({
    type: {
      allowBot: Boolean,
      allowMultimedia: Boolean,
      allowEmpty: Boolean,
      maxMessageLength: Number,
    },
    required: true,
  })
  messageFilters: {
    allowBot: boolean;
    allowMultimedia: boolean;
    allowEmpty: boolean;
    maxMessageLength: number;
  };
  @Prop({
    type: {
      gameToTelegram: Boolean,
      telegramToGame: Boolean,
    },
    required: true,
  })
  routingRules: {
    gameToTelegram: boolean;
    telegramToGame: boolean;
  };
}
export const MultiGroupConfigSchema = SchemaFactory.createForClass(MultiGroupConfig);
export type MultiGroupConfigDocument = MultiGroupConfig & Document;
</file>

<file path="src/utils/basic.utils.ts">
import { httpCode } from '../constant/http.constant';
import { ErrorInfo } from './error.utils';
export class BasicUtils {
  static checkAddressType(address: string) {
    return address.startsWith('bc1p');
  }
  static okResponse<T>(data?: T) {
    return {
      code: httpCode.OK.code,
      msg: httpCode.OK.msg,
      data: data,
    };
  }
  static errorResponse<T>(error: { code: number; msg: string }, data?: T) {
    return {
      code: error.code,
      msg: error.msg,
      data: data,
    };
  }
  static errorResponseWithCodeMsg<T>(code: number, msg: string, data?: T) {
    return {
      code: code,
      msg: msg,
      data: data,
    };
  }
  static createErrorResponse(errorInfo: ErrorInfo) {
    return {
      code: errorInfo.code,
      msg: errorInfo.message,
      type: errorInfo.type,
      retryable: errorInfo.retryable,
      timestamp: errorInfo.timestamp,
    };
  }
  static isResponseOk(resp: any) {
    return resp.code == httpCode.OK.code;
  }
  static unisatResponseOK(resp: any) {
    return resp.code == httpCode.UNISAT_OK.code;
  }
}
</file>

<file path="src/message/message.module.ts">
import { Module } from '@nestjs/common';
import { MessageService } from './message.service';
import { RedisModule } from '../redis/redis.module';
@Module({
  imports: [RedisModule],
  providers: [MessageService],
  exports: [MessageService],
})
export class MessageModule {}
</file>

<file path="src/routes/telegram-bot/telegram-bot.controller.ts">
import { Controller, Get, Post } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TelegramBotService } from './telegram-bot.service';
@ApiTags('telegram-bot')
@Controller('telegram-bot')
export class TelegramBotController {
  constructor(private readonly telegramBotService: TelegramBotService) {}
}
</file>

<file path="src/routes/telegram-bot/telegram-bot.module.ts">
import { Module} from '@nestjs/common';
import { TelegramBotService } from './telegram-bot.service';
import { MongooseModule } from '@nestjs/mongoose';
import { MultiGroupConfig, MultiGroupConfigSchema } from "../../schemas/multi-group-config.schema";
import { TelegramService } from '../telegram/telegram.service';
import { MessageModule } from "../../message/message.module";
import { BullModule } from "@nestjs/bull";
@Module({
  imports: [
    MongooseModule.forFeature([{ name: MultiGroupConfig.name, schema: MultiGroupConfigSchema }]),
    MessageModule,
    BullModule.registerQueue({
      name: 'tg-chat-sync-game-queue'
    })
  ],
  providers: [TelegramBotService,TelegramService],
  exports: [TelegramBotService,TelegramService],
})
export class TelegramBotModule {}
</file>

<file path="src/routes/telegram/telegram.controller.ts">
import { Controller, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SyncDto } from '../../dto/sync.dto';
import { TelegramService } from './telegram.service';
@ApiTags('telegram')
@Controller('telegram')
export class TelegramController {
  constructor(private readonly telegramService: TelegramService) {}
  @Post('sync-to-game')
  @ApiOperation({
    summary: '推送 Telegram 消息到游戏',
    description: `
      将 Telegram 群组主题帖中的消息同步推送到对应的游戏聊天窗口。
      ## 功能特性
      - 支持普通消息和回复消息的同步
      - 自动解析和构建回复链信息
      - 支持多层回复深度追踪
      - 自动映射 Telegram 群组到游戏频道
      - 支持主题帖消息同步
      ## 回复链处理
      系统会自动分析回复关系，构建完整的回复链路径，
      并在游戏中正确显示回复层级关系。
    `,
  })
  @ApiResponse({ status: 201, description: '消息同步成功，已推送到目标游戏频道' })
  @ApiResponse({ status: 400, description: '请求参数错误，请检查消息格式和必填字段' })
  @ApiResponse({ status: 500, description: '服务器内部错误，消息同步失败' })
  async syncToGame(@Body() dto: SyncDto) {
    return await this.telegramService.syncToGame(dto);
  }
}
</file>

<file path="config/multi-group-mappings.online.json">
[
  {
    "telegramGroupId": "@UniWorldsHQ",
    "gameChatChannelId": 1001,
    "telegramThreadId": "44502",
    "telegramListenThreadIds": [
      "1",
      "86"
    ],
    "enabled": true,
    "priority": 1,
    "description": "UniWorldsHQ正式群",
    "adminConfig": {
      "adminUsernames": [
        "@SatWorld_io",
        "@Bob_Cheung"
      ]
    },
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": false,
      "allowEmpty": false,
      "maxMessageLength": 4000
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@wangcai_fractal",
    "gameChatChannelId": 1003,
    "enabled": true,
    "priority": 1,
    "description": "wangcai社区群",
    "adminConfig": {
      "adminUsernames": [
        "@livf2",
        "@topbit8x8",
        "@tedestar",
        "@wangcai636",
        "@A_Ilian",
        "@Betty_LYL",
        "@lorenzonically",
        "@daima66"
      ]
    },
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": false,
      "allowEmpty": false,
      "maxMessageLength": 4000
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@fractal_bitcoin_official",
    "gameChatChannelId": 1002,
    "enabled": true,
    "priority": 1,
    "description": "fractal",
    "adminConfig": {
      "adminUsernames": [
        "@ranaom1",
        "@fractal_comm",
        "@joshua_jh",
        "@cloudxy99",
        "@slothnvivian",
        "@Betty_LYL",
        "@lorenzonically",
        "@xiaoning_nan",
        "@spenceryang"
      ]
    },
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": false,
      "allowEmpty": false,
      "maxMessageLength": 4000
    },
    "routingRules": {
      "gameToTelegram": false,
      "telegramToGame": true
    }
  }
]
</file>

<file path=".env.example">
# Telegram 配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
# 使用群组用户名，从 https://t.me/grassPush/1 链接中提取
TELEGRAM_GROUP_ID=@grassPush
TELEGRAM_THREAD_ID=1

# 游戏聊天 API 配置
GAME_CHAT_API_URL=http://localhost:8080
# 1001 - 主聊天窗口, 1002 - 系统消息窗口, 1 - 默认窗口
GAME_CHAT_CHANNEL_ID=1001

# Redis配置(集群版本)
#REDIS_TYPE=cluster
#REDIS_NODES=
#REDIS_PORT=
#REDIS_PASSWORD=
#REDIS_DATABASE=

# Redis配置(主备版本)
REDIS_TYPE=single
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DATABASE=1

# Redis MQ配置
MQ_REDIS_URL=redis://localhost:6379
MQ_REDIS_PASSWORD=
MQ_REDIS_DATABASE=2

# 服务配置
PORT=3000
NODE_ENV=development

# Telegram Bot 配置
FORCE_REAL_MODE=true
DISABLE_TEST_MODE=true

# 管理员配置（逗号分隔的用户名列表，支持带@或不带@的格式）
TELEGRAM_ADMIN_USERNAMES=@admin1,admin2,@admin3

# 多群组配置（可选）
# 启用多群组模式时，可以配置多个Telegram群组对应不同的游戏聊天频道
# 格式：JSON数组，每个对象包含telegramGroupId、gameChatChannelId和可选的telegramThreadId
# MULTI_GROUP_MAPPINGS=[{"telegramGroupId":"@group1","gameChatChannelId":"1001","telegramThreadId":"1"},{"telegramGroupId":"@group2","gameChatChannelId":"1002","telegramThreadId":"2"}]

# 代理配置（可选）
HTTP_PROXY=http://127.0.0.1:7897
HTTPS_PROXY=http://127.0.0.1:7897
SOCKS_PROXY=socks5://127.0.0.1:7897

BOT_INSTANCE_ID=unique_instance_id
MAX_BOT_RETRIES=3
BOT_RESTART_DELAY=10000
</file>

<file path="src/dto/sync.dto.ts">
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
export class SyncDto {
  @ApiProperty({ description: '发送者昵称' })
  @IsString()
  @IsNotEmpty()
  fromUser: string;
  @ApiProperty({ description: '消息内容' })
  @IsString()
  @IsNotEmpty()
  content: string;
  @ApiProperty({ description: '消息时间戳' })
  @IsNumber()
  @Type(() => Number)
  timestamp: number;
  @ApiProperty({ description: '聊天ID（可选）', required: false })
  @IsOptional()
  @IsString()
  chatId?: string;
  @ApiProperty({ description: '是否为管理员', required: false })
  @IsOptional()
  @Type(() => Boolean)
  isAdmin?: boolean;
  @ApiPropertyOptional({ description: '被回复消息的ID' })
  @IsOptional()
  @IsString()
  replyTo?: string;
  @ApiProperty({ description: '消息ID' })
  @IsString()
  @IsNotEmpty()
  messageId: string;
  @ApiPropertyOptional({ description: '消息来源平台' })
  @IsOptional()
  @IsString()
  sourcePlatform?: string;
  @ApiPropertyOptional({ description: '主题帖ID（Telegram论坛群组）' })
  @IsOptional()
  @IsString()
  threadId?: string;
  @ApiPropertyOptional({ description: '原始消息ID（用于映射存储）' })
  @IsOptional()
  @IsString()
  originalMessageId?: string;
  @ApiPropertyOptional({ description: '是否为系统消息', default: false })
  @IsOptional()
  @IsBoolean()
  isSystem?: boolean;
}
export class ThreadMessagesQueryDto {
  @ApiPropertyOptional({ description: '返回消息数量', default: 20 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 20;
  @ApiPropertyOptional({ description: '某时间戳之前的消息' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  before?: number;
}
</file>

<file path="src/routes/game/handler/game-to-telegram.handle.ts">
import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { jobs, queues } from '../../../constant/mq.constant';
import { Job } from 'bull';
import { GameService } from '../game.service';
@Injectable()
@Processor(queues.GAME_CHAT_SYNC_TG_QUEUE)
export class GameToTelegramHandle {
  private readonly logger: Logger = new Logger(GameToTelegramHandle.name);
  constructor(private readonly gameService: GameService) {}
  @Process(jobs.GAME_CHAT_SYNC_TG_JOB)
  async handleTask(job: Job) {
    const { message, targetMapping } = job.data;
    this.logger.log(
      `开始处理游戏消息推送任务: ${job.id}, 消息ID: ${message?.messageId}, 目标群组: ${targetMapping?.telegramGroupId || '默认'}`,
    );
    await this.gameService.handleSyncToTelegram({ message, targetMapping });
    this.logger.log(`游戏消息推送任务完成: ${job.id}`);
  }
}
</file>

<file path="package.json">
{
  "name": "tg-chat-sync-bot",
  "version": "0.0.1",
  "description": "",
  "author": "",
  "private": true,
  "license": "UNLICENSED",
  "scripts": {
    "build": "nest build",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "start:real": "node start-real-mode.js",
    "test:telegram": "node test/test-real-telegram.js",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json"
  },
  "dependencies": {
    "@nestjs-modules/ioredis": "^2.0.2",
    "@nestjs/bull": "^11.0.2",
    "@nestjs/common": "^11.0.1",
    "@nestjs/config": "^4.0.2",
    "@nestjs/core": "^11.0.1",
    "@nestjs/mongoose": "^11.0.3",
    "@nestjs/platform-express": "^11.0.1",
    "@nestjs/swagger": "^11.2.0",
    "axios": "^1.9.0",
    "bull": "^4.16.5",
    "class-transformer": "^0.5.1",
    "class-validator": "^0.14.2",
    "dotenv": "^16.5.0",
    "mongoose": "^8.15.1",
    "reflect-metadata": "^0.2.2",
    "rxjs": "^7.8.1",
    "telegraf": "^4.16.3"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3.2.0",
    "@eslint/js": "^9.18.0",
    "@nestjs/cli": "^11.0.0",
    "@nestjs/schematics": "^11.0.0",
    "@nestjs/testing": "^11.0.1",
    "@swc/cli": "^0.6.0",
    "@swc/core": "^1.10.7",
    "@types/express": "^5.0.0",
    "@types/jest": "^29.5.14",
    "@types/node": "^22.10.7",
    "@types/supertest": "^6.0.2",
    "eslint": "^9.18.0",
    "eslint-config-prettier": "^10.0.1",
    "eslint-plugin-prettier": "^5.2.2",
    "globals": "^16.0.0",
    "jest": "^29.7.0",
    "prettier": "^3.4.2",
    "source-map-support": "^0.5.21",
    "supertest": "^7.0.0",
    "ts-jest": "^29.2.5",
    "ts-loader": "^9.5.2",
    "ts-node": "^10.9.2",
    "tsconfig-paths": "^4.2.0",
    "typescript": "^5.7.3",
    "typescript-eslint": "^8.20.0"
  },
  "jest": {
    "moduleFileExtensions": [
      "js",
      "json",
      "ts"
    ],
    "rootDir": "src",
    "testRegex": ".*\\.spec\\.ts$",
    "transform": {
      "^.+\\.(t|j)s$": "ts-jest"
    },
    "collectCoverageFrom": [
      "**/*.(t|j)s"
    ],
    "coverageDirectory": "../coverage",
    "testEnvironment": "node"
  }
}
</file>

<file path="README.md">
# Telegram-游戏聊天同步系统

<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

一个基于 NestJS 框架的 Telegram 群组主题帖与游戏聊天窗口双向消息同步系统。

## ✨ 主要特性

- 🔄 **双向消息同步**：Telegram ↔ 游戏聊天频道
- 🏢 **多群组支持**：支持多个Telegram群组映射到不同游戏频道
- 📝 **论坛主题帖支持**：完美支持Telegram论坛群组的主题帖功能
- 🚀 **高性能队列系统**：基于Redis和Bull队列，支持消息重试和错误处理
- 🛡️ **安全可靠**：完整的错误处理、消息去重、管理员权限控制
- 🎭 **智能消息过滤**：自动过滤表情符号、多媒体内容，确保消息兼容性
- 📊 **监控友好**：详细的日志记录、健康检查接口
- 🔧 **易于部署**：Docker支持、环境变量配置、自动化测试

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件配置必要参数：

```env
# Telegram Bot 配置
TELEGRAM_BOT_TOKEN=你的机器人Token

# 单群组模式配置（传统方式）
TELEGRAM_GROUP_ID=@grassPush  # 或使用数字ID：*************
TELEGRAM_THREAD_ID=1
GAME_CHAT_CHANNEL_ID=1001

# 多群组模式配置（可选，配置后将启用多群组模式）
# MULTI_GROUP_MAPPINGS=[{"telegramGroupId":"@group1","gameChatChannelId":"1001","telegramThreadId":"1","enabled":true,"priority":1,"messageFilters":{"allowBot":false,"allowMultimedia":false,"allowEmpty":false,"maxMessageLength":1000},"routingRules":{"gameToTelegram":true,"telegramToGame":true}},{"telegramGroupId":"@group2","gameChatChannelId":"1002","enabled":true,"priority":2}]

# 游戏API配置
GAME_CHAT_API_URL=游戏聊天API地址
GAME_CHAT_API_TOKEN=游戏API授权Token

# Redis配置（用于消息队列）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 服务配置
PORT=3000
```

#### 如何获取群组信息？

**方法1：从 Telegram 链接获取（推荐）**
如果您有群组链接如：`https://t.me/grassPush/1`

- 群组用户名：`grassPush` → 配置为 `@grassPush`
- 主题帖ID：`1` → 直接使用 `1`

**方法2：使用机器人获取群组ID**

1. 将机器人添加到群组
2. 让机器人发送消息
3. 使用 Telegram Bot API 获取更新信息

### 启动服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### 验证安装

运行集成测试脚本：

```bash
node test-integration.js
```

访问健康检查接口：

```bash
curl http://localhost:3000/health
```

## 🧪 测试

- 单元测试：`npm run test`
- 端到端测试：`npm run test:e2e`
- 集成测试：`node test-integration.js`（需服务运行中）
- Bot 监听测试：`node test-bot-listener.js`

集成测试将验证：环境配置、API健康、Bot连接、消息同步、历史消息、重复消息处理等。

Bot 监听测试将验证：Bot 状态、自动监听、消息接收、服务重启等。

## 🤖 Bot 监听功能

- 应用启动后自动监听配置的 Telegram 群组
- 轮询频率：每秒检查一次新消息
- 支持主题帖过滤和自动同步到游戏 API
- 管理接口：
  - `GET /telegram-bot/status` - 查看监听状态
  - `POST /telegram-bot/restart` - 重启监听服务

## 🏢 多群组配置详解

系统支持两种配置模式：单群组模式（传统）和多群组模式（增强）。

### 单群组模式

使用传统的环境变量配置：

```env
TELEGRAM_GROUP_ID=@grassPush
TELEGRAM_THREAD_ID=1
GAME_CHAT_CHANNEL_ID=1001
```

### 多群组模式

通过 `MULTI_GROUP_MAPPINGS` 环境变量配置多个群组映射：

```env
MULTI_GROUP_MAPPINGS=[
  {
    "telegramGroupId": "@group1",
    "gameChatChannelId": "1001",
    "telegramThreadId": "1",
    "enabled": true,
    "priority": 1,
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": false,
      "allowEmpty": false,
      "maxMessageLength": 1000
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@group2",
    "gameChatChannelId": "1002",
    "enabled": true,
    "priority": 2
  }
]
```

### 配置参数说明

| 参数                      | 类型     | 必填 | 默认值 | 说明                                 |
| ------------------------- | -------- | ---- | ------ | ------------------------------------ |
| `telegramGroupId`         | string   | ✅   | -      | Telegram群组ID（@用户名或数字ID）    |
| `gameChatChannelId`       | number   | ✅   | -      | 游戏聊天频道ID                       |
| `telegramThreadId`        | string   | ❌   | -      | 游戏消息推送到Telegram的目标主题帖ID |
| `telegramListenThreadIds` | string[] | ❌   | -      | Telegram消息监听的主题帖ID列表       |
| `enabled`                 | boolean  | ❌   | true   | 是否启用此映射                       |
| `priority`                | number   | ❌   | 0      | 优先级（数字越小优先级越高）         |
| `messageFilters`          | object   | ❌   | 见下表 | 消息过滤规则                         |
| `routingRules`            | object   | ❌   | 见下表 | 路由规则                             |

### 消息过滤规则 (messageFilters)

| 参数               | 类型    | 默认值 | 说明               |
| ------------------ | ------- | ------ | ------------------ |
| `allowBot`         | boolean | false  | 是否允许Bot消息    |
| `allowMultimedia`  | boolean | false  | 是否允许多媒体消息 |
| `allowEmpty`       | boolean | false  | 是否允许空消息     |
| `maxMessageLength` | number  | 1000   | 最大消息长度       |

### 路由规则 (routingRules)

| 参数             | 类型    | 默认值 | 说明                       |
| ---------------- | ------- | ------ | -------------------------- |
| `gameToTelegram` | boolean | true   | 允许游戏消息推送到Telegram |
| `telegramToGame` | boolean | true   | 允许Telegram消息推送到游戏 |

### 配置示例

**基础多群组配置：**

```json
[
  {
    "telegramGroupId": "@maingroup",
    "gameChatChannelId": 1001
  },
  {
    "telegramGroupId": "-*************",
    "gameChatChannelId": 1002,
    "telegramThreadId": "5"
  }
]
```

**高级配置示例：**

```json
[
  {
    "telegramGroupId": "@vipgroup",
    "gameChatChannelId": 1001,
    "telegramThreadId": "183",
    "telegramListenThreadIds": ["1", "8"],
    "enabled": true,
    "priority": 1,
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": true,
      "maxMessageLength": 500
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@readonly",
    "gameChatChannelId": 1002,
    "priority": 2,
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": false
    }
  }
]
```

## 🎭 消息过滤功能

系统内置智能消息过滤功能，确保消息推送的稳定性和兼容性：

### 支持的过滤类型

- **表情符号过滤**：自动移除Unicode表情符号（😊🌞🎉等）
- **多媒体内容检测**：跳过照片、视频、贴纸、文档等多媒体消息
- **纯表情消息过滤**：跳过只包含表情符号的消息
- **HTML标签清理**：移除潜在的恶意HTML标签
- **消息长度限制**：根据配置限制消息最大长度
- **Bot消息过滤**：可配置是否允许Bot发送的消息

### 过滤示例

```
原始消息："你好😊今天天气不错🌞"
过滤后："你好今天天气不错"

原始消息："😊😂🤣😍🥰"
处理结果：消息被跳过（纯表情消息）

长消息：超过maxMessageLength限制的消息将被截断或跳过
```

### 测试过滤功能

```bash
# 构建项目
npm run build

# 运行过滤功能测试
node test/test-emoji-filter.js
```

## 📖 API 文档

启动服务后访问：http://localhost:3000/api-docs

## 🔧 核心接口

### Telegram 相关接口

- `POST /telegram/sync-to-game` - Telegram 消息同步到游戏
- `GET /telegram/thread-messages` - 获取 Telegram 主题帖历史消息
- `GET /telegram/health` - Telegram 服务健康检查

### 游戏相关接口

- `POST /game/sync-to-telegram` - 游戏消息同步到 Telegram

### 管理员接口

- `GET /admin/ad-filter/config` - 获取广告过滤配置
- `PUT /admin/ad-filter/config` - 更新广告过滤配置
- `POST /admin/ad-filter/keywords` - 添加广告关键词
- `DELETE /admin/ad-filter/keywords` - 删除广告关键词
- `GET /admin/ad-filter/keywords` - 获取所有关键词
- `POST /admin/ad-filter/whitelist` - 添加白名单用户
- `DELETE /admin/ad-filter/whitelist` - 删除白名单用户
- `GET /admin/ad-filter/whitelist` - 获取白名单
- `POST /admin/ad-filter/test` - 测试消息过滤
- `GET /admin/ad-filter/stats` - 获取过滤统计
- `POST /admin/ad-filter/stats/reset` - 重置过滤统计

### Bot 管理接口

- `GET /telegram-bot/status` - 获取 Bot 监听状态
- `POST /telegram-bot/restart` - 重启 Bot 监听

### 广告过滤管理接口

- `GET /admin/ad-filter/config` - 获取广告过滤配置
- `PUT /admin/ad-filter/config` - 更新广告过滤配置
- `POST /admin/ad-filter/keywords` - 添加广告关键词
- `DELETE /admin/ad-filter/keywords` - 移除广告关键词
- `POST /admin/ad-filter/whitelist` - 添加白名单关键词
- `POST /admin/ad-filter/test` - 测试消息广告过滤
- `GET /admin/ad-filter/stats` - 获取广告过滤统计
- `POST /admin/ad-filter/reset` - 重置广告过滤配置
- `POST /admin/ad-filter/toggle` - 切换广告过滤开关

### 应用基础接口

- `GET /app/hello` - 基础测试接口

### 请求示例

```bash
# 同步 Telegram 消息到游戏
curl -X POST "http://localhost:3000/telegram/sync-to-game" \
  -H "Content-Type: application/json" \
  -d '{
    "messageId": "12345",
    "username": "用户名",
    "content": "消息内容",
    "timestamp": 1640995200
  }'

# 获取广告过滤配置
curl -X GET "http://localhost:3000/admin/ad-filter/config"

# 更新广告过滤配置
curl -X PUT "http://localhost:3000/admin/ad-filter/config" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "adThreshold": 0.3,
    "enableCapsDetection": true,
    "keywords": ["广告", "推广"],
    "whitelist": ["官方"]
  }'

# 测试消息广告过滤
curl -X POST "http://localhost:3000/admin/ad-filter/test" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这是一条测试消息"
  }'

# 添加广告关键词
curl -X POST "http://localhost:3000/admin/ad-filter/keywords" \
  -H "Content-Type: application/json" \
  -d '{
    "keywords": ["新广告词", "推销"]
  }'

# 同步游戏消息到 Telegram
curl -X POST "http://localhost:3000/game/sync-to-telegram" \
  -H "Content-Type: application/json" \
  -d '{
    "messageId": "game_12345",
    "fromUser": "玩家名",
    "content": "Hello, Telegram!",
    "timestamp": 1672531200000
  }'
```

## 📂 项目结构

```
src/
├── app.controller.ts          # 应用基础控制器
├── app.module.ts              # 主模块
├── app.service.ts             # 应用基础服务
├── main.ts                    # 应用启动入口
├── config/                    # 配置管理模块
│   ├── config.module.ts
│   └── config.service.ts      # 环境变量和多群组配置
├── dto/                       # 数据传输对象
│   └── sync.dto.ts           # 消息同步DTO
├── message/                   # 消息处理中心
│   ├── message.module.ts
│   └── message.service.ts     # 消息格式化和验证
├── redis/                     # Redis和队列服务
│   ├── redis.module.ts
│   └── redis.service.ts
├── routes/                    # 路由模块
│   ├── admin/                 # 管理员接口
│   │   ├── admin.controller.ts
│   │   ├── admin.module.ts
│   │   └── admin.service.ts
│   ├── game/                  # 游戏相关接口
│   │   ├── game.controller.ts
│   │   ├── game.module.ts
│   │   ├── game.service.ts
│   │   └── handler/           # 消息队列处理器
│   ├── telegram/              # Telegram相关接口
│   │   ├── telegram.controller.ts
│   │   ├── telegram.module.ts
│   │   ├── telegram.service.ts
│   │   └── handler/           # 消息队列处理器
│   └── telegram-bot/          # Bot管理接口
│       ├── telegram-bot.controller.ts
│       ├── telegram-bot.module.ts
│       └── telegram-bot.service.ts
├── telegram/                  # Telegram核心服务
│   └── telegram.service.ts
├── utils/                     # 工具函数
│   ├── basic.utils.ts
│   └── error.utils.ts
└── constant/                  # 常量定义
    ├── http.constant.ts
    ├── magic.constant.ts
    └── mq.constant.ts
test/
├── test-integration.js        # 集成测试脚本
├── test-bot-listener.js       # Bot 监听测试脚本
├── test-game-to-telegram-push.js # 游戏到TG推送测试
└── ...                        # 其他测试脚本
```

## 📋 开发指南

### 📚 完整文档索引

- **[文档索引](./docs/README.md)** - 所有技术文档的完整索引和导航
- **[系统架构完整说明](./docs/系统架构完整说明.md)** - 系统整体架构设计和模块划分
- **[API文档说明](./docs/API文档说明.md)** - 完整的 RESTful API 接口文档
- **[项目开发与交付文档](./docs/项目开发与交付文档.md)** - 开发环境搭建和部署指南

### 🔧 功能特性文档

- **[多群组聊天同步配置说明](./docs/多群组聊天同步配置说明.md)** - 多群组配置和管理
- **[广告过滤功能说明](./docs/广告过滤功能说明.md)** - 智能广告检测和过滤
- **[消息过滤功能说明](./docs/消息过滤功能说明.md)** - 消息过滤规则和配置
- **[消息回复功能实现说明](./docs/消息回复功能实现说明.md)** - 回复链构建和处理
- **[游戏消息推送到Telegram实现说明](./docs/游戏消息推送到Telegram实现说明.md)** - 消息同步机制

## 🛠️ 技术栈

### 🏗️ 核心框架
- **框架**：NestJS 10.x + TypeScript 5.x
- **运行时**：Node.js 18+
- **包管理**：npm

### 📖 API 文档
- **文档生成**：Swagger/OpenAPI 3.0
- **接口测试**：内置 Swagger UI

### ✅ 数据验证
- **参数验证**：class-validator + class-transformer
- **DTO 转换**：自动类型转换和验证

### ⚙️ 配置管理
- **环境变量**：dotenv + 自定义配置服务
- **多群组配置**：JSON 文件 + 动态加载
- **配置验证**：启动时配置完整性检查

### 🔄 消息队列
- **队列系统**：Bull 4.x (基于 Redis)
- **任务调度**：支持延迟、重试和优先级
- **监控面板**：Bull Dashboard (可选)

### 🌐 网络通信
- **HTTP 客户端**：Axios + 连接池
- **Telegram API**：node-telegram-bot-api
- **WebHook 支持**：Express 中间件

### 💾 数据存储
- **缓存**：Redis 7.x
- **配置存储**：JSON 文件
- **日志存储**：文件系统 + 控制台输出

### 🧪 测试框架
- **单元测试**：Jest + Supertest
- **集成测试**：自定义测试脚本
- **覆盖率**：Jest Coverage

### 📏 代码质量
- **代码规范**：ESLint + Prettier
- **类型检查**：TypeScript 严格模式
- **Git 钩子**：Husky + lint-staged

### 🔧 开发工具
- **热重载**：NestJS CLI + nodemon
- **调试支持**：VS Code 调试配置
- **构建工具**：TypeScript 编译器

### 🐳 部署支持
- **容器化**：Docker + Docker Compose
- **进程管理**：PM2 (可选)
- **环境隔离**：多环境配置支持

## 部署说明

- 推荐参考 [NestJS 部署文档](https://docs.nestjs.com/deployment)
- 可选用 [NestJS Mau](https://mau.nestjs.com) 平台一键部署到 AWS

```bash
npm install -g @nestjs/mau
mau deploy
```

## 📚 参考链接

- [NestJS 官方文档](https://docs.nestjs.com)
- [NestJS Mau 云部署](https://mau.nestjs.com)
- [NestJS Devtools](https://devtools.nestjs.com)
- [官方 Discord 社区](https://discord.gg/G7Qnnhy)

## License

MIT License
</file>

<file path="src/app.module.ts">
import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MessageModule } from './message/message.module';
import { TelegramModule } from './routes/telegram/telegram.module';
import { GameModule } from './routes/game/game.module';
import { TelegramBotModule } from './routes/telegram-bot/telegram-bot.module';
import { RedisModule } from './redis/redis.module';
import { MultiGroupConfigModule } from './routes/multi-group-config/multi-group-config.module';
import { MongooseModule } from '@nestjs/mongoose';
@Module({
  imports: [
    MongooseModule.forRoot(process.env.MONGODB_URI || ""),
    MessageModule,
    TelegramModule,
    TelegramBotModule,
    GameModule,
    RedisModule,
    MultiGroupConfigModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
</file>

<file path="config/multi-group-mappings.json">
[
  {
    "telegramGroupId": "@grassPush",
    "gameChatChannelId": 1001,
    "telegramThreadId": "44502",
    "telegramListenThreadIds": [
      "1",
      "86"
    ],
    "enabled": true,
    "priority": 1,
    "description": "1001推送群组",
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": false,
      "allowEmpty": false,
      "maxMessageLength": 1000
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@testFractalPush",
    "gameChatChannelId": 1002,
    "enabled": true,
    "priority": 2,
    "description": "1002推送群组",
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": true,
      "allowEmpty": false,
      "maxMessageLength": 500
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  }
]
</file>

<file path="src/main.ts">
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as dotenv from 'dotenv';
dotenv.config();
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const config = new DocumentBuilder()
    .setTitle('Telegram-游戏聊天同步系统')
    .setDescription('实现 Telegram 群组主题帖与游戏聊天窗口的双向消息同步，包含广告过滤管理功能')
    .setVersion('1.0')
    .addTag('api', '外部 API 接口')
    .addTag('telegram', 'Telegram 相关接口')
    .addTag('game', '游戏相关接口')
    .addTag('telegram-bot', 'Telegram Bot 管理接口')
    .addTag('admin', '广告过滤管理接口')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);
  const port = process.env.PORT || 3000;
  await app.listen(port, '0.0.0.0');
  console.log(`🚀 应用程序启动成功！`);
  console.log(`🌐 服务地址: http://localhost:${port}`);
  console.log(`📚 API 文档: http://localhost:${port}/api-docs`);
  console.log(`🔧 运行环境: ${process.env.NODE_ENV === 'development' ? '开发模式' : '生产模式'}`);
}
bootstrap();
</file>

<file path="src/routes/telegram/telegram.service.ts">
import { Injectable, Logger } from '@nestjs/common';
import { SyncDto } from '../../dto/sync.dto';
import { FormattedMessage, MessageService } from '../../message/message.service';
import { BasicUtils } from '../../utils/basic.utils';
import { httpCode } from '../../constant/http.constant';
import { InjectQueue } from '@nestjs/bull';
import { jobs, queues } from '../../constant/mq.constant';
import { Queue } from 'bull';
import axios from 'axios';
import { ErrorUtils } from '../../utils/error.utils';
import { InjectModel } from '@nestjs/mongoose';
import { MultiGroupConfig, MultiGroupConfigDocument } from '../../schemas/multi-group-config.schema';
import { Model } from 'mongoose';
@Injectable()
export class TelegramService {
  private readonly logger = new Logger(TelegramService.name);
  private gameApiUrl: string;
  constructor(
    private readonly messageService: MessageService,
    @InjectQueue(queues.TG_CHAT_SYNC_GAME_QUEUE) private readonly telegram2GameQueue: Queue,
    @InjectModel(MultiGroupConfig.name) private readonly multiGroupConfig: Model<MultiGroupConfigDocument>,
  ) {
    this.gameApiUrl = process.env.GAME_CHAT_API_URL || "";
  }
  async syncToGame(dto: SyncDto) {
    const { content } = dto;
    this.logger.log(`syncToGame dto: ${JSON.stringify(dto)}`);
    try {
      if (this.messageService.isAdvertisement(dto.content)) {
        this.logger.warn(
          `检测到广告消息，阻止推送到游戏: ${dto.messageId} - ${dto.fromUser}: ${dto.content.substring(0, 100)}...`,
        );
        return BasicUtils.okResponse({ message: '消息已接收，但因包含广告内容未推送到游戏' });
      }
      const validation = this.messageService.validateMessage(dto);
      if (!validation.isValid) {
        if (validation.error && validation.error.includes('广告')) {
          this.logger.warn(`广告内容验证失败，阻止推送: ${dto.messageId} - ${validation.error}`);
          return BasicUtils.okResponse({ message: '消息已接收，但因包含广告内容未推送到游戏' });
        }
        return BasicUtils.errorResponseWithCodeMsg(httpCode.FAILED.code, validation.error || 'invalid message');
      }
      const formattedMessage = this.messageService.formatTelegramToGame(dto);
      if (dto.replyTo) {
        const gameReplyId = await this.messageService.getMessageMapping(dto.replyTo, 'telegram');
        if (gameReplyId) {
          formattedMessage.replyTo = gameReplyId;
          this.logger.log(`映射回复消息ID: Telegram${dto.replyTo} -> 游戏${gameReplyId}`);
        } else {
          this.logger.warn(`未找到回复消息的映射: Telegram${dto.replyTo}`);
          formattedMessage.replyTo = dto.replyTo;
        }
      }
      await this.telegram2GameQueue.add(jobs.TG_CHAT_SYNC_GAME_JOB, {
        message: formattedMessage,
        telegramGroupId: dto.chatId,
      });
      return BasicUtils.okResponse();
    } catch (error) {
      const errorInfo = ErrorUtils.formatError(error, '同步Telegram消息到游戏', {
        messageId: dto.messageId,
        fromUser: dto.fromUser,
        chatId: dto.chatId,
        operation: 'syncToGame',
      });
      ErrorUtils.logError(this.logger, errorInfo, dto.messageId);
      return BasicUtils.createErrorResponse(errorInfo);
    }
  }
  async handleSyncToGame(message: FormattedMessage, telegramGroupId: string) {
    this.logger.log(`同步Telegram消息到游戏: ${JSON.stringify(message)}`);
    if (telegramGroupId) {
      this.logger.debug(`消息来源群组: ${telegramGroupId}`);
    }
    const multiGroupConfig = await this.multiGroupConfig
      .findOne({
        telegramGroupId,
      })
      .lean();
    if (!multiGroupConfig) {
      return;
    }
    const targetChannelId = multiGroupConfig.gameChatChannelId;
    this.logger.debug(`目标游戏频道ID: ${targetChannelId}`);
    const response = await axios.post(
      `${this.gameApiUrl}/api/chat/message`,
      {
        chatId: targetChannelId,
        content: message.content,
        playerId: message.fromUser,
        isAdmin: message.isAdmin || false,
        replyTo: message.replyTo,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      },
    );
    this.logger.log(`游戏API响应: ${response.status} - ${JSON.stringify(response.data)}`);
    if (response.data && response.data.messageId) {
      const gameMessageId = response.data.messageId.toString();
      await this.messageService.storeMessageMapping(message.messageId, gameMessageId, 'telegram');
      await this.messageService.storeMessageMapping(gameMessageId, message.messageId, 'game');
      this.logger.debug(`存储消息映射: Telegram${message.messageId} <-> 游戏${gameMessageId}`);
    }
  }
}
</file>

<file path="src/message/message.service.ts">
import { Injectable, Logger } from '@nestjs/common';
import { SyncDto } from '../dto/sync.dto';
import { RedisService } from '../redis/redis.service';
import MagicConstant from '../constant/magic.constant';
import { MessageType } from '../enum/message-type.enum';
export interface FormattedMessage {
  messageId: string;
  fromUser: string;
  content: string;
  timestamp: number;
  source: 'telegram' | 'game';
  isAdmin?: boolean;
  replyTo?: string;
  threadId?: string;
  messageType?: 'text' | MessageType;
}
@Injectable()
export class MessageService {
  private readonly logger = new Logger(MessageService.name);
  constructor(private readonly redisService: RedisService) {}
  formatTelegramToGame(dto: SyncDto): FormattedMessage {
    return {
      messageId: dto.messageId,
      fromUser: this.sanitizeTelegramUsername(dto.fromUser),
      content: dto.content,
      timestamp: dto.timestamp,
      source: 'telegram',
      isAdmin: dto.isAdmin,
      threadId: dto.threadId,
    };
  }
  formatGameToTelegram(dto: SyncDto): FormattedMessage {
    return {
      messageId: dto.messageId,
      fromUser: dto.fromUser,
      content: dto.content,
      timestamp: dto.timestamp,
      source: 'game',
      isAdmin: dto.isAdmin,
      threadId: dto.threadId,
    };
  }
  validateMessage(dto: SyncDto): { isValid: boolean; error?: string } {
    if (!dto.messageId || dto.messageId.trim() === '') {
      return { isValid: false, error: '消息ID不能为空' };
    }
    if (!dto.fromUser || dto.fromUser.trim() === '') {
      return { isValid: false, error: '发送者昵称不能为空' };
    }
    if (!dto.content || dto.content.trim() === '') {
      return { isValid: false, error: '消息内容不能为空' };
    }
    if (!dto.timestamp || dto.timestamp <= 0) {
      return { isValid: false, error: '消息时间戳无效' };
    }
    // 检查消息长度限制
    if (dto.content.length > 4000) {
      return { isValid: false, error: '消息内容过长，最大支持4000字符' };
    }
    return { isValid: true };
  }
  /**
   * 专门处理Telegram用户名，优先保留真实姓名
   * @param username Telegram用户显示名称
   */
  private sanitizeTelegramUsername(username: string): string {
    // 对于Telegram用户名，保留更多字符，包括空格和常见标点
    // 移除危险字符，但保留中文、英文、数字、空格、常见标点
    return username
      .replace(/[<>"'&\\]/g, '') // 移除可能危险的字符
      .replace(/\s+/g, ' ') // 规范化空格
      .trim()
      .substring(0, 50);
  }
  // /**
  //  * 广告过滤功能
  //  * @param content 原始内容
  //  */
  // private filterAdvertisements(content: string): string {
  //   return content;
  // }
  /**
   * 检查消息是否为广告
   * @param content 消息内容
   * @returns 是否为广告
   */
  isAdvertisement(content: string): boolean {
    return !!content;
  }
  /**
   * 过滤表情符号
   * @param content 原始内容
   */
  private filterEmojis(content: string): string {
    // 移除Unicode表情符号
    // 包括：基本表情、扩展表情、符号、装饰符号等
    return content
      .replace(/[\u{1F600}-\u{1F64F}]/gu, '') // 表情符号
      .replace(/[\u{1F300}-\u{1F5FF}]/gu, '') // 杂项符号和象形文字
      .replace(/[\u{1F680}-\u{1F6FF}]/gu, '') // 交通和地图符号
      .replace(/[\u{1F1E0}-\u{1F1FF}]/gu, '') // 区域指示符号
      .replace(/[\u{2600}-\u{26FF}]/gu, '') // 杂项符号
      .replace(/[\u{2700}-\u{27BF}]/gu, '') // 装饰符号
      .replace(/[\u{1F900}-\u{1F9FF}]/gu, '') // 补充符号和象形文字
      .replace(/[\u{1FA70}-\u{1FAFF}]/gu, '') // 扩展A符号和象形文字
      .replace(/[\u{FE00}-\u{FE0F}]/gu, '') // 变体选择器
      .replace(/[\u{200D}]/gu, '') // 零宽连接符
      .trim();
  }
  /**
   * 标记消息已处理
   * @param messageId 消息ID
   */
  async markMessageAsProcessed(messageId: string): Promise<void> {
    // TODO: 实现消息处理状态记录（可以使用缓存或数据库）
    // 这里可以集成 Redis 或其他缓存方案来记录已处理的消息
    // 示例实现：可以将消息ID存储到缓存中，设置过期时间
    // 实际项目中建议使用 Redis 或数据库来持久化存储
  }
  /**
   * 存储消息映射关系
   * @param originalId 原始消息ID
   * @param mappedId 映射后的消息ID
   * @param platform 平台类型
   */
  async storeMessageMapping(originalId: string, mappedId: string, platform: 'telegram' | 'game'): Promise<void> {
    const key = `message_mapping:${platform}:${originalId}`;
    await this.redisService.setWithExp(key, mappedId, MagicConstant.SEVEN_DAYS_SECONDS);
    this.logger.debug(`存储消息映射: ${originalId} -> ${mappedId} (${platform})`);
  }
  async getMessageMapping(originalId: string, platform: 'telegram' | 'game'): Promise<string | null> {
    const key = `message_mapping:${platform}:${originalId}`;
    const mappedId = await this.redisService.get(key);
    this.logger.debug(`获取消息映射: ${originalId} -> ${mappedId} (${platform})`);
    return mappedId;
  }
}
</file>

<file path="src/routes/telegram-bot/telegram-bot.service.ts">
import { Injectable } from '@nestjs/common';
import { Telegraf, Context } from 'telegraf';
import { SyncDto } from '../../dto/sync.dto';
import { BasicUtils } from '../../utils/basic.utils';
import { Model } from 'mongoose';
import { MultiGroupConfig } from '../../schemas/multi-group-config.schema';
import { InjectModel } from '@nestjs/mongoose';
@Injectable()
export class TelegramBotService {
  private bot: Telegraf;
  logger: any;
  TelegramService: any;
  constructor(
    @InjectModel(MultiGroupConfig.name)
    private readonly multiGroupConfigModel: Model<MultiGroupConfig>,
  ) {
    const botToken = process.env.TELEGRAM_BOT_TOKEN || "";
    if (!botToken) {
      throw new Error("TELEGRAM_BOT_TOKEN未配置");
    }
    this.bot = new Telegraf(botToken);
    this.setupBot();
  }
  private setupBot() {
    this.bot.start((ctx) => {
      ctx.reply('欢迎使用SatWorld ChatSync Bot!');
    });
    this.bot.on('message', async (ctx) => {
      const message = ctx.message;
      if (message.message_id) {
        await this.handleMessage(ctx);
      }
    });
  }
  private async handleMessage(msg: Context) {
    try {
      const chatId = msg.chat?.id
      const chatUsername = msg.from?.username ? `@${msg.from.username}` : null;
      const actualGroupId = chatUsername || chatId;
      const targetMapping = await this.multiGroupConfigModel.findOne({
        $or: [
          { telegramGroupId: actualGroupId },
          { gameChatChannelId: actualGroupId },
        ],
      })
      if (!targetMapping) {
        this.logger.debug(`消息不是来自任何配置的群组 ${actualGroupId}，跳过处理`);
        return;
      }
      if (targetMapping.enabled === false) {
        this.logger.debug(`群组 ${actualGroupId} 已禁用，跳过处理`);
        return;
      }
      if (!(targetMapping as any).telegramToGame) {
        this.logger.debug(`群组 ${actualGroupId} 未配置telegramToGame路由规则，跳过处理`);
        return;
      }
      this.logger.debug(`消息来自群组 ${actualGroupId}，映射到游戏频道 ${targetMapping.gameChatChannelId}`);
      if (targetMapping.telegramListenThreadIds && targetMapping.telegramListenThreadIds.length > 0) {
        const messageThreadId = (msg as any).message_thread_id
        const isFromListenedThread = targetMapping.telegramListenThreadIds.includes(messageThreadId);
        const isDefaultThread = !(msg as any).message_thread_id && targetMapping.telegramListenThreadIds.includes('1');
        if (!isFromListenedThread && !isDefaultThread) {
          this.logger.debug(
            `消息不是来自监听的主题帖列表 ${targetMapping.telegramListenThreadIds.join(', ')}，` +
            `消息thread_id: ${messageThreadId}，跳过处理`,
          );
          return;
        }
        this.logger.debug(`消息来自监听的主题帖 ${messageThreadId}，处理中...`);
      }
      else if (targetMapping.telegramThreadId && targetMapping.telegramThreadId !== '0') {
        const messageThreadId = (msg as any).message_thread_id?.toString();
        const configuredThreadId = targetMapping.telegramThreadId;
        if (configuredThreadId === '1') {
          const isThread1 = !messageThreadId || messageThreadId === '1';
          if (!isThread1) {
            this.logger.debug(`消息不是来自主题帖1，消息thread_id: ${messageThreadId || 'undefined'}，跳过处理`);
            return;
          }
        } else {
          if (messageThreadId !== configuredThreadId) {
            this.logger.debug(
              `消息不是来自目标主题帖 ${configuredThreadId}，消息thread_id: ${messageThreadId || 'undefined'}，跳过处理`,
            );
            return;
          }
        }
      }
      if (!this.checkMessageFilters(targetMapping, msg)) {
        this.logger.debug(`消息未通过群组 ${actualGroupId} 的过滤规则，跳过处理`);
        return;
      }
      if (msg.from?.is_bot) {
        this.logger.debug('跳过 Bot 消息');
        return;
      }
      if (this.isMultimediaMessage(msg)) {
        this.logger.debug('跳过多媒体消息（照片、视频、贴纸、文档等）');
        return;
      }
      if (!msg.text || msg.text.trim() === '') {
        this.logger.debug('跳过非文本消息');
        return;
      }
      // 检查消息是否只包含表情符号
      if (this.isOnlyEmojis(msg.text)) {
        this.logger.debug('跳过纯表情消息');
        return;
      }
      // 获取用户名用于管理员检查
      const username = msg.from?.username || '';
      // 优先使用真实姓名，格式：姓名 或 姓+名
      const firstName = msg.from?.first_name || '';
      const lastName = msg.from?.last_name || '';
      const displayName = lastName ? `${firstName} ${lastName}`.trim() : firstName || msg.from?.username || '未知用户';
      // 检查是否为管理员（使用数据库多群组配置）
      const isAdmin = this.checkAdminPermission(targetMapping, username, msg.from?.id?.toString());
      // 构造同步消息对象
      const syncDto: SyncDto = {
        messageId: `tg_${(msg as any).message_id}`,
        fromUser: displayName,
        content: msg.text,
        timestamp: ((msg as any).date || Date.now() / 1000) * 1000, // 转换为毫秒
        chatId: actualGroupId?.toString(), // 使用格式化后的群组ID，支持多群组模式
        isAdmin: isAdmin,
        replyTo: (msg as any).message_thread_id?.toString(),
        sourcePlatform: 'telegram',
      };
      this.logger.log(`接收到 Telegram 消息: [${syncDto.fromUser}] ${syncDto.content}`);
      const result = await this.TelegramService.syncToGame(syncDto);
      if (BasicUtils.isResponseOk(result)) {
        this.logger.log(`消息同步到游戏成功: ${syncDto.messageId}`);
      } else {
        this.logger.error(`消息同步到游戏失败: ${result.msg}`);
      }
    } catch (error: unknown) {
      this.logger.error('处理 Telegram 消息失败', error);
    }
  }
  async sendMessage(chatId: string, text: string, threadId?: string, extra?: any) {
    const options: any = {
      parse_mode: 'HTML',
      ...extra,
    };
    if (threadId) {
      options.message_thread_id = threadId;
    }
    return this.bot.telegram.sendMessage(chatId, text, options);
  }
  private checkAdminPermission(targetMapping: MultiGroupConfig, username: string, userId?: string): boolean {
    const adminUsers = targetMapping.adminConfig.adminUsers;
    if (!Array.isArray(adminUsers) || adminUsers.length === 0) {
      return false;
    }
    const adminSet = new Set(adminUsers);
    return adminSet.has(username) || adminSet.has(userId || '');
  }
  /**
   * 检查是否为多媒体消息
   * @param msg Telegram消息对象
   */
  private isMultimediaMessage(msg: any): boolean {
    return !!(
      msg.photo ||
      msg.video ||
      msg.sticker ||
      msg.document ||
      msg.animation ||
      msg.voice ||
      msg.audio ||
      msg.video_note ||
      msg.location ||
      msg.contact ||
      msg.poll ||
      msg.venue
    );
  }
  /**
   * 检查消息是否只包含表情符号
   * @param text 消息文本
   */
  private isOnlyEmojis(text: string): boolean {
    // 移除所有表情符号后检查是否还有其他内容
    const textWithoutEmojis = text
      .replace(/[\u{1F600}-\u{1F64F}]/gu, '') // 表情符号
      .replace(/[\u{1F300}-\u{1F5FF}]/gu, '') // 杂项符号和象形文字
      .replace(/[\u{1F680}-\u{1F6FF}]/gu, '') // 交通和地图符号
      .replace(/[\u{1F1E0}-\u{1F1FF}]/gu, '') // 区域指示符号
      .replace(/[\u{2600}-\u{26FF}]/gu, '') // 杂项符号
      .replace(/[\u{2700}-\u{27BF}]/gu, '') // 装饰符号
      .replace(/[\u{1F900}-\u{1F9FF}]/gu, '') // 补充符号和象形文字
      .replace(/[\u{1FA70}-\u{1FAFF}]/gu, '') // 扩展A符号和象形文字
      .replace(/[\u{FE00}-\u{FE0F}]/gu, '') // 变体选择器
      .replace(/[\u{200D}]/gu, '') // 零宽连接符
      .replace(/\s/g, '') // 移除空白字符
      .trim();
    return textWithoutEmojis.length === 0;
  }
  private checkMessageFilters(targetMapping: any, msg: any): boolean {
    // 如果没有配置过滤规则，默认通过
    if (!targetMapping.filters) {
      return true;
    }
    const filters = targetMapping.filters;
    // 检查消息类型过滤
    if (filters.allowedTypes && filters.allowedTypes.length > 0) {
      const messageType = this.getMessageType(msg);
      if (!filters.allowedTypes.includes(messageType)) {
        return false;
      }
    }
    return true;
  }
  private getMessageType(msg: any): string {
    if (msg.text) return 'text';
    if (msg.photo) return 'photo';
    if (msg.video) return 'video';
    return 'other';
  }
  async startBot() {
    await this.bot.launch();
    console.log('Telegram机器人已启动');
  }
  async stopBot() {
    this.bot.stop('SIGTERM');
  }
}
</file>

<file path="src/routes/game/game.service.ts">
import { Injectable, Logger } from '@nestjs/common';
import { SyncDto } from '../../dto/sync.dto';
import { MessageService } from '../../message/message.service';
import { BasicUtils } from '../../utils/basic.utils';
import { ErrorUtils } from '../../utils/error.utils';
import { jobs, queues } from '../../constant/mq.constant';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import axios from 'axios';
import { InjectModel } from '@nestjs/mongoose';
import { MultiGroupConfig } from '../../schemas/multi-group-config.schema';
import { Model } from 'mongoose';
interface TargetMapping {
  telegramGroupId: string;
  gameChatChannelId: number;
  telegramThreadId?: string;
  enabled?: boolean;
  priority?: number;
}
@Injectable()
export class GameService {
  private readonly logger = new Logger(GameService.name);
  private static readonly DEFAULT_RETRY_DELAY = 30;
  private static readonly REQUEST_TIMEOUT = 10000;
  private static readonly CHAT_INFO_TIMEOUT = 5000;
  private botToken: string;
  constructor(
    private readonly messageService: MessageService,
    @InjectQueue(queues.GAME_CHAT_SYNC_TG_QUEUE) private readonly game2TelegramQueue: Queue,
    @InjectModel(MultiGroupConfig.name) private readonly multiGroupConfig: Model<MultiGroupConfig>,
  ) {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN || "";
  }
  async syncToTelegram(dto: SyncDto) {
    this.logger.log(`syncToTelegram dto: ${JSON.stringify(dto)}`);
    try {
      const formattedMessage = this.messageService.formatGameToTelegram(dto);
      this.logger.debug(`syncToTelegram 格式化消息: ${JSON.stringify(formattedMessage)}`);
      const gameChatChannelId = dto.chatId ? parseInt(dto.chatId, 10) : 0;
      this.logger.debug(`syncToTelegram 游戏频道ID: ${gameChatChannelId}`);
      const multiGroupConfig = await this.multiGroupConfig
        .findOne({
          gameChatChannelId,
        })
        .lean();
      if (!multiGroupConfig) {
        return;
      }
      this.logger.debug(`syncToTelegram 群组映射: ${JSON.stringify(multiGroupConfig)}`);
      const targetMapping: TargetMapping = {
        telegramGroupId: multiGroupConfig.telegramGroupId,
        gameChatChannelId: multiGroupConfig.gameChatChannelId,
        telegramThreadId: multiGroupConfig.telegramThreadId,
        enabled: multiGroupConfig.enabled,
        priority: multiGroupConfig.priority,
      };
      this.logger.debug(`syncToTelegram 目标映射: ${JSON.stringify(targetMapping)}`);
      await this.game2TelegramQueue.add(
        jobs.GAME_CHAT_SYNC_TG_JOB,
        {
          message: formattedMessage,
          targetMapping: targetMapping,
        },
        {
          priority: targetMapping.priority || 1,
          delay: 0,
          jobId: `${formattedMessage.messageId}-${targetMapping.telegramGroupId}`,
        },
      );
      this.logger.log(`游戏消息已加入推送队列: ${formattedMessage.messageId}`);
      return BasicUtils.okResponse();
    } catch (error) {
      const errorInfo = ErrorUtils.formatError(error, '游戏消息推送到队列', {
        messageId: dto?.messageId,
        operation: 'syncToTelegram',
      });
      ErrorUtils.logError(this.logger, errorInfo, dto?.messageId);
      return BasicUtils.createErrorResponse(errorInfo);
    }
  }
  private async getChatInfo(groupId: string): Promise<{ chatId: number; isForumGroup: boolean }> {
    const getChatApiUrl = `https://api.telegram.org/bot${this.botToken}/getChat`;
    try {
      const chatInfoResponse = await axios.get(getChatApiUrl, {
        params: { chat_id: groupId },
        timeout: GameService.CHAT_INFO_TIMEOUT,
      });
      if (chatInfoResponse.data.ok && chatInfoResponse.data.result) {
        const result = chatInfoResponse.data.result;
        const chatId = result.id;
        const isForumGroup = result.is_forum === true;
        if (groupId.startsWith('@')) {
          this.logger.log(`成功解析群组ID: ${chatId}，论坛模式: ${isForumGroup}`);
        }
        return { chatId, isForumGroup };
      } else {
        const errorMsg = groupId.startsWith('@')
          ? `无法解析Telegram用户名 ${groupId} 为数字ID`
          : `无法获取群组 ${groupId} 的信息`;
        this.logger.error(`${errorMsg}。响应: ${JSON.stringify(chatInfoResponse.data)}`);
        throw new Error(errorMsg);
      }
    } catch (error) {
      if (groupId.startsWith('@')) {
        this.logger.error(`调用getChat API获取 ${groupId} 的数字ID失败: ${error.message}`);
        throw error;
      } else {
        const parsedId = parseInt(groupId, 10);
        if (!isNaN(parsedId)) {
          this.logger.warn(`获取群组状态失败: ${error.message}，假定为普通群组`);
          return { chatId: parsedId, isForumGroup: false };
        } else {
          this.logger.error(`无效的群组ID格式: ${groupId}。期望得到一个纯数字ID或者以@开头的用户名。`);
          throw new Error(`无效的群组ID格式: ${groupId}`);
        }
      }
    }
  }
  private extractRetryAfterTime(errorMessage: string): number {
    const match = errorMessage.match(/retry after (\d+)/i);
    if (match && match[1]) {
      return parseInt(match[1], 10);
    }
    return GameService.DEFAULT_RETRY_DELAY;
  }
  async handleSyncToTelegram(data: any, maxRetries: number = 3, currentRetry: number = 0): Promise<void> {
    const { message, targetMapping } = data;
    this.logger.log(`[尝试次数: ${currentRetry + 1}/${maxRetries}] 开始推送游戏消息到Telegram: ${message.messageId}`);
    let requestBody: any = {};
    const telegramApiUrl = `https://api.telegram.org/bot${this.botToken}/sendMessage`;
    try {
      let groupId: string;
      let threadIdEnv: string | undefined;
      if (targetMapping) {
        groupId = targetMapping.telegramGroupId;
        threadIdEnv = targetMapping.telegramThreadId;
        if (targetMapping.enabled === false) {
          this.logger.warn(`群组 ${groupId} 已禁用，跳过推送`);
          return;
        }
        this.logger.log(
          `推送到群组: ${groupId}，主题ID: ${threadIdEnv || '默认'}，优先级: ${targetMapping.priority || 1}`,
        );
      } else {
        this.logger.error('目标映射配置未提供，无法推送消息。');
        throw new Error('目标映射配置未提供');
      }
      if (!groupId) {
        this.logger.error('Telegram 群组 ID 未配置，无法推送消息。');
        throw new Error('Telegram 群组 ID 未配置');
      }
      const fromUser = message.fromUser.slice(0, 5) + '...' + message.fromUser.slice(-5);
      let messageText = `🎮 [${fromUser}]: ${message.content}`;
      if (message.replyToUser && message.replyToContent) {
        const replyInfo = this.formatReplyInfo(message.replyToUser, message.replyToContent, message.replyDepth || 0);
        messageText = `🎮 [${fromUser}]: ${replyInfo}\n\n${message.content}`;
      }
      const { chatId: numericChatId, isForumGroup } = await this.getChatInfo(groupId);
      requestBody = {
        chat_id: numericChatId,
        text: messageText,
        parse_mode: 'HTML',
      };
      if (message.replyToMessageId) {
        const telegramReplyId = await this.messageService.getMessageMapping(message.replyToMessageId, 'game');
        if (telegramReplyId) {
          requestBody.reply_to_message_id = parseInt(telegramReplyId, 10);
          this.logger.log(`设置回复消息ID: ${message.replyToMessageId} -> ${telegramReplyId}`);
        } else {
          this.logger.warn(`未找到回复消息的映射: ${message.replyToMessageId}`);
        }
      }
      if (isForumGroup) {
        let targetThreadId: number;
        if (threadIdEnv && threadIdEnv.trim() !== '') {
          const parsedThreadId = parseInt(threadIdEnv, 10);
          if (!isNaN(parsedThreadId) && parsedThreadId > 0) {
            targetThreadId = parsedThreadId;
            // 只有当主题帖ID大于1时才添加 message_thread_id 参数
            if (parsedThreadId > 1) {
              requestBody.message_thread_id = targetThreadId;
              this.logger.log(`论坛模式：使用配置的主题帖ID (${targetThreadId}) 发送消息。`);
            } else {
              this.logger.log(
                `论坛模式：主题帖ID为 ${parsedThreadId}，不添加 message_thread_id 参数，发送到默认主题帖。`,
              );
            }
          } else {
            this.logger.warn(`论坛模式：主题帖ID (${threadIdEnv}) 配置无效，不添加 message_thread_id 参数。`);
          }
        } else {
          this.logger.log(`论坛模式：主题帖ID未配置，不添加 message_thread_id 参数，发送到默认主题帖。`);
        }
      }
      const response = await axios.post(telegramApiUrl, requestBody, {
        headers: { 'Content-Type': 'application/json' },
        timeout: GameService.REQUEST_TIMEOUT,
      });
      if (response.data.ok) {
        this.logger.log(`游戏消息推送成功: ${message.messageId}`);
        const telegramMessageId = response.data.result.message_id.toString();
        await this.messageService.storeMessageMapping(message.messageId, telegramMessageId, 'game');
        await this.messageService.storeMessageMapping(telegramMessageId, message.messageId, 'telegram');
        this.logger.debug(`存储消息映射: 游戏${message.messageId} <-> Telegram${telegramMessageId}`);
        await this.messageService.markMessageAsProcessed(message.messageId);
      } else {
        const telegramError = `Telegram API返回错误: ${response.data.description} (错误码: ${response.data.error_code})`;
        this.logger.error(telegramError);
        throw new Error(telegramError);
      }
    } catch (error) {
      const errorInfo = ErrorUtils.formatError(error, '推送消息到Telegram', {
        messageId: message.messageId,
        attempt: currentRetry + 1,
        maxRetries,
        telegramApiUrl,
        requestBody: JSON.stringify(requestBody),
        operation: 'handleSyncToTelegram',
      });
      ErrorUtils.logError(this.logger, errorInfo, message.messageId);
      if (errorInfo.retryable && currentRetry < maxRetries - 1) {
        const delayMs = ErrorUtils.getRetryDelay(error);
        this.logger.warn(
          `[${message.messageId}] 🔄 将在 ${Math.round(delayMs / 1000)} 秒后进行第 ${currentRetry + 2} 次重试`,
        );
        await new Promise((resolve) => setTimeout(resolve, delayMs));
        return this.handleSyncToTelegram(data, maxRetries, currentRetry + 1);
      }
      this.logger.error(
        `[${message.messageId}] ❌ 推送失败: ${errorInfo.retryable ? '已达到最大重试次数' : '不可重试的错误'}`,
      );
      throw error;
    }
  }
  private formatReplyInfo(replyToUser: string, replyToContent: string, replyDepth: number): string {
    const maxContentLength = 50;
    const truncatedContent =
      replyToContent.length > maxContentLength ? `${replyToContent.substring(0, maxContentLength)}...` : replyToContent;
    const depthPrefix = replyDepth > 0 ? `[${'└'.repeat(Math.min(replyDepth, 3))}] ` : '';
    const replyPrefix = replyDepth > 3 ? `[深度${replyDepth}] ` : depthPrefix;
    return `${replyPrefix}回复 @${replyToUser}: ${truncatedContent}`;
  }
}
</file>

</files>
