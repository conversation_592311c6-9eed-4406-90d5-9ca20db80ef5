import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 消息过滤配置
 */
@Schema({ _id: false })
export class MessageFilter {
  @Prop({ default: true })
  allowBot: boolean;

  @Prop({ default: false })
  allowMultimedia: boolean;

  @Prop({ default: false })
  allowEmpty: boolean;

  @Prop({ default: 1000 })
  maxMessageLength: number;

  @Prop({ default: true })
  enableAdFilter: boolean;

  @Prop({ type: [String], default: [] })
  adKeywords: string[];

  @Prop({ type: [String], default: [] })
  sensitiveWords: string[];
}

/**
 * 管理员配置
 */
@Schema({ _id: false })
export class AdminConfig {
  @Prop({ type: [String], default: [] })
  adminUsers: string[];

  @Prop({ default: false })
  adminOnly: boolean;
}

/**
 * 统一的同步配置Schema
 * 合并了原来的多个配置表
 */
@Schema({ collection: 'sync_configs', timestamps: true })
export class SyncConfig extends Document {
  @Prop({ required: true, unique: true })
  telegramGroupId: string;

  @Prop({ required: true, unique: true })
  gameChatChannelId: number;

  @Prop()
  telegramThreadId?: string;

  @Prop({ type: [String], default: [] })
  telegramListenThreadIds: string[];

  @Prop({ required: true, default: true })
  enabled: boolean;

  @Prop({ required: true, default: 1 })
  priority: number;

  @Prop({ required: true })
  description: string;

  @Prop({ type: AdminConfig, default: () => ({}) })
  adminConfig: AdminConfig;

  @Prop({ type: MessageFilter, default: () => ({}) })
  messageFilter: MessageFilter;

  @Prop({ default: 'bidirectional' })
  syncDirection: 'telegram_to_game' | 'game_to_telegram' | 'bidirectional';

  @Prop({ default: 0 })
  messageCount: number;

  @Prop()
  lastSyncAt?: Date;

  @Prop()
  lastErrorAt?: Date;

  @Prop()
  lastErrorMessage?: string;

  @Prop({ default: true })
  enableRetry: boolean;

  @Prop({ default: 3 })
  maxRetries: number;

  @Prop({ default: 30 })
  retryDelaySeconds: number;

  @Prop()
  notes?: string;

  @Prop()
  createdBy?: string;

  @Prop()
  updatedBy?: string;
}

export const SyncConfigSchema = SchemaFactory.createForClass(SyncConfig);
export type SyncConfigDocument = SyncConfig & Document;

// 创建索引
SyncConfigSchema.index({ telegramGroupId: 1 });
SyncConfigSchema.index({ gameChatChannelId: 1 });
SyncConfigSchema.index({ enabled: 1, priority: 1 });
SyncConfigSchema.index({ lastSyncAt: 1 });
