import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class TelegramService {
  private readonly logger = new Logger(TelegramService.name);
  private readonly botToken: string;
  private readonly apiUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN || '';
    this.apiUrl = `https://api.telegram.org/bot${this.botToken}`;
  }

  /**
   * 发送消息到Telegram群组
   */
  async sendMessage(
    chatId: string, 
    message: string, 
    threadId?: string,
    options?: any
  ): Promise<boolean> {
    try {
      if (!this.botToken) {
        this.logger.error('Telegram Bot Token未配置');
        return false;
      }

      const payload: any = {
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML',
        ...options,
      };

      // 如果指定了线程ID，添加到请求中
      if (threadId) {
        payload.message_thread_id = parseInt(threadId);
      }

      const response: AxiosResponse = await firstValueFrom(
        this.httpService.post(`${this.apiUrl}/sendMessage`, payload, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );

      if (response.data.ok) {
        this.logger.log(`消息发送成功到Telegram群组 ${chatId}: ${message.substring(0, 50)}...`);
        return true;
      } else {
        this.logger.warn(`Telegram API返回错误:`, response.data);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送消息到Telegram失败 (群组 ${chatId}):`, error);
      return false;
    }
  }

  /**
   * 获取群组信息
   */
  async getChatInfo(chatId: string): Promise<any> {
    try {
      if (!this.botToken) {
        this.logger.error('Telegram Bot Token未配置');
        return null;
      }

      const response: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.apiUrl}/getChat`, {
          params: { chat_id: chatId },
          timeout: 5000,
        })
      );

      if (response.data.ok) {
        return response.data.result;
      }
      
      return null;
    } catch (error) {
      this.logger.error(`获取Telegram群组信息失败 (群组 ${chatId}):`, error);
      return null;
    }
  }

  /**
   * 检查Bot状态
   */
  async checkBotStatus(): Promise<boolean> {
    try {
      if (!this.botToken) {
        return false;
      }

      const response: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.apiUrl}/getMe`, {
          timeout: 5000,
        })
      );

      return response.data.ok;
    } catch (error) {
      this.logger.error('检查Telegram Bot状态失败:', error);
      return false;
    }
  }

  /**
   * 获取Bot信息
   */
  async getBotInfo(): Promise<any> {
    try {
      if (!this.botToken) {
        return null;
      }

      const response: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.apiUrl}/getMe`, {
          timeout: 5000,
        })
      );

      if (response.data.ok) {
        return response.data.result;
      }
      
      return null;
    } catch (error) {
      this.logger.error('获取Telegram Bot信息失败:', error);
      return null;
    }
  }

  /**
   * 格式化消息内容
   */
  formatMessage(content: string, fromUser?: string): string {
    if (!content) return '';

    // 转义HTML特殊字符
    let formatted = content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    // 添加用户前缀
    if (fromUser) {
      formatted = `<b>[${this.escapeHtml(fromUser)}]</b> ${formatted}`;
    }

    return formatted.trim();
  }

  /**
   * 转义HTML字符
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * 验证群组ID格式
   */
  isValidChatId(chatId: any): boolean {
    if (!chatId) return false;
    
    const id = String(chatId);
    // 支持 @username 格式或数字ID
    return id.startsWith('@') || /^-?\d+$/.test(id);
  }

  /**
   * 验证线程ID
   */
  isValidThreadId(threadId: any): boolean {
    if (!threadId) return true; // 线程ID是可选的
    
    const id = parseInt(threadId);
    return !isNaN(id) && id > 0;
  }

  /**
   * 获取配置信息
   */
  getConfigInfo() {
    return {
      botConfigured: !!this.botToken,
      apiUrl: this.apiUrl.replace(this.botToken, '***'),
    };
  }

  /**
   * 发送文件到Telegram
   */
  async sendDocument(
    chatId: string,
    document: any,
    caption?: string,
    threadId?: string
  ): Promise<boolean> {
    try {
      if (!this.botToken) {
        this.logger.error('Telegram Bot Token未配置');
        return false;
      }

      const payload: any = {
        chat_id: chatId,
        document,
        caption: caption || '',
      };

      if (threadId) {
        payload.message_thread_id = parseInt(threadId);
      }

      const response: AxiosResponse = await firstValueFrom(
        this.httpService.post(`${this.apiUrl}/sendDocument`, payload, {
          timeout: 30000,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
      );

      if (response.data.ok) {
        this.logger.log(`文件发送成功到Telegram群组 ${chatId}`);
        return true;
      } else {
        this.logger.warn(`Telegram API返回错误:`, response.data);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送文件到Telegram失败 (群组 ${chatId}):`, error);
      return false;
    }
  }

  /**
   * 处理同步到游戏的消息
   */
  async handleSyncToGame(message: any, telegramGroupId: string): Promise<void> {
    try {
      this.logger.log(`处理Telegram消息同步到游戏: ${message?.messageId}`);

      // 这里应该实现具体的同步逻辑
      // 暂时只记录日志
      this.logger.log(`消息内容: ${message?.content}`);
      this.logger.log(`来源群组: ${telegramGroupId}`);

    } catch (error) {
      this.logger.error('处理Telegram消息同步失败:', error);
      throw error;
    }
  }
}
