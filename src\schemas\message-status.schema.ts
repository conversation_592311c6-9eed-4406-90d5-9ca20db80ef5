import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 简化的消息状态Schema
 */
@Schema({ collection: 'message_status', timestamps: true })
export class MessageStatus extends Document {
  @Prop({ required: true, index: true })
  messageId: string;

  @Prop({ required: true, enum: ['processed', 'failed', 'pending', 'sent', 'filtered'], default: 'processed' })
  status: string;

  @Prop({ required: true, enum: ['telegram', 'game'] })
  source: string;

  @Prop({ required: true, enum: ['telegram', 'game'] })
  target: string;

  @Prop({ required: true })
  chatId: string;

  @Prop()
  threadId?: string;

  @Prop({ required: true })
  fromUser: string;

  @Prop()
  content?: string;

  @Prop()
  errorMessage?: string;

  @Prop()
  retryCount?: number;

  @Prop()
  mappedMessageId?: string;
}

export const MessageStatusSchema = SchemaFactory.createForClass(MessageStatus);
export type MessageStatusDocument = MessageStatus & Document;

// 创建索引
MessageStatusSchema.index({ messageId: 1, source: 1 });
MessageStatusSchema.index({ status: 1, createdAt: 1 });
MessageStatusSchema.index({ chatId: 1, createdAt: -1 });

  @Prop({ required: false })
  content?: string; // 消息内容（可选，用于调试）

  @Prop({ required: false })
  errorMessage?: string; // 错误信息（如果处理失败）

  @Prop({ required: false })
  timestamp?: number; // 原始消息时间戳

  @Prop({
    type: {
      retryCount: { type: Number, default: 0 },
      lastRetryAt: { type: Date },
      maxRetries: { type: Number, default: 3 },
    },
    default: {
      retryCount: 0,
      maxRetries: 3,
    },
  })
  retryInfo!: {
    retryCount: number;
    lastRetryAt?: Date;
    maxRetries: number;
  };

  @Prop({
    type: {
      processingTime: { type: Number }, // 处理耗时（毫秒）
      queueTime: { type: Number }, // 队列等待时间（毫秒）
      totalTime: { type: Number }, // 总耗时（毫秒）
    },
    default: {},
  })
  performance?: {
    processingTime?: number;
    queueTime?: number;
    totalTime?: number;
  };

  @Prop({
    type: {
      version: String,
      description: String,
      tags: [String],
    },
    default: {
      version: '2.0.0',
      description: '消息处理状态记录 - 重构版本',
      tags: ['messaging', 'status'],
    },
  })
  metadata!: {
    version: string;
    description: string;
    tags?: string[];
  };
}

export const MessageStatusSchema = SchemaFactory.createForClass(MessageStatusDocument);

// 创建索引以提高查询性能
MessageStatusSchema.index({ processedAt: 1 });
MessageStatusSchema.index({ status: 1 });
MessageStatusSchema.index({ source: 1 });
MessageStatusSchema.index({ chatId: 1 });
MessageStatusSchema.index({ timestamp: 1 });
MessageStatusSchema.index({ 'retryInfo.retryCount': 1 });
