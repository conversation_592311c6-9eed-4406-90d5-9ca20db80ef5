import { Module } from '@nestjs/common';
import { RedisModule as NestRedisModule } from '@nestjs-modules/ioredis';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigController } from './config.controller';
import { ConfigService } from './config.service';
import { SyncConfig, SyncConfigSchema } from '../schemas/sync-config.schema';

@Module({
  imports: [
    // Redis配置
    NestRedisModule.forRoot({
      type: 'single',
      url: `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || 6379}`,
      options: {
        password: process.env.REDIS_PASSWORD,
        maxRetriesPerRequest: 3,
      },
    }),
    // MongoDB配置
    MongooseModule.forFeature([
      { name: SyncConfig.name, schema: SyncConfigSchema },
    ]),
  ],
  controllers: [ConfigController],
  providers: [ConfigService],
  exports: [ConfigService, NestRedisModule],
})
export class ConfigModule {}
