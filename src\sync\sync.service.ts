import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';
import { Model } from 'mongoose';
import { Queue } from 'bull';
import { Telegraf } from 'telegraf';
import axios from 'axios';
import { SyncDto } from '../dto/sync.dto';
import { MessageService } from './message.service';
import { ConfigService } from './config.service';
import { SyncConfig } from '../schemas/sync-config.schema';
import { MessageStatus } from '../schemas/message-status.schema';
import { BasicUtils } from '../utils/basic.utils';
import { SimpleErrorUtils } from '../utils/simple-error.utils';
import { queues, jobs } from '../constant/mq.constant';

@Injectable()
export class SyncService implements OnModuleInit {
  private readonly logger = new Logger(SyncService.name);
  private bot: Telegraf;
  private isListening = false;

  constructor(
    private readonly messageService: MessageService,
    private readonly configService: ConfigService,
    @InjectModel(SyncConfig.name) private readonly syncConfigModel: Model<SyncConfig>,
    @InjectModel(MessageStatus.name) private readonly messageStatusModel: Model<MessageStatus>,
    @InjectQueue(queues.MESSAGE_SYNC_QUEUE) private readonly syncQueue: Queue,
  ) {
    this.bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN || '');
    this.setupBot();
  }

  async onModuleInit() {
    await this.startBotListening();
    this.setupQueueProcessor();
  }

  /**
   * 同步Telegram消息到游戏
   */
  async syncTelegramToGame(dto: SyncDto) {
    try {
      // 获取配置
      const config = await this.configService.getConfigByChatId(dto.chatId);
      if (!config || !config.enabled) {
        return BasicUtils.errorResponseWithCodeMsg(400, '群组未配置或已禁用');
      }

      // 格式化消息
      const formattedMessage = this.messageService.formatTelegramToGame(dto);
      
      // 处理回复消息映射
      if (dto.replyTo) {
        const gameReplyId = await this.messageService.getMessageMapping(dto.replyTo, 'telegram');
        if (gameReplyId) {
          formattedMessage.replyTo = gameReplyId;
        }
      }

      // 添加到队列
      await this.syncQueue.add(jobs.TELEGRAM_TO_GAME, {
        message: formattedMessage,
        config: config,
      });

      return BasicUtils.okResponse();
    } catch (error) {
      this.logger.error('同步Telegram消息到游戏失败', error);
      return BasicUtils.errorResponseWithCodeMsg(500, '同步失败');
    }
  }

  /**
   * 同步游戏消息到Telegram
   */
  async syncGameToTelegram(dto: SyncDto) {
    try {
      // 获取配置
      const config = await this.configService.getConfigByGameChannelId(dto.chatId);
      if (!config || !config.enabled) {
        return BasicUtils.errorResponseWithCodeMsg(400, '频道未配置或已禁用');
      }

      // 格式化消息
      const formattedMessage = this.messageService.formatGameToTelegram(dto);
      
      // 处理回复消息映射
      if (dto.replyTo) {
        const telegramReplyId = await this.messageService.getMessageMapping(dto.replyTo, 'game');
        if (telegramReplyId) {
          formattedMessage.replyTo = telegramReplyId;
        }
      }

      // 添加到队列
      await this.syncQueue.add(jobs.GAME_TO_TELEGRAM, {
        message: formattedMessage,
        config: config,
      });

      return BasicUtils.okResponse();
    } catch (error) {
      this.logger.error('同步游戏消息到Telegram失败', error);
      return BasicUtils.errorResponseWithCodeMsg(500, '同步失败');
    }
  }

  /**
   * 获取Bot状态
   */
  async getBotStatus() {
    return BasicUtils.okResponse({
      isListening: this.isListening,
      botUsername: this.bot.botInfo?.username,
      uptime: process.uptime(),
    });
  }

  /**
   * 重启Bot监听
   */
  async restartBot() {
    try {
      await this.stopBotListening();
      await this.startBotListening();
      return BasicUtils.okResponse({ message: 'Bot重启成功' });
    } catch (error) {
      this.logger.error('重启Bot失败', error);
      return BasicUtils.errorResponseWithCodeMsg(500, '重启失败');
    }
  }

  /**
   * 获取同步配置
   */
  async getConfigs() {
    try {
      const configs = await this.syncConfigModel.find({ enabled: true }).exec();
      return BasicUtils.okResponse(configs);
    } catch (error) {
      this.logger.error('获取配置失败', error);
      return BasicUtils.errorResponseWithCodeMsg(500, '获取配置失败');
    }
  }

  /**
   * 更新同步配置
   */
  async updateConfig(config: any) {
    try {
      const result = await this.syncConfigModel.findOneAndUpdate(
        { telegramGroupId: config.telegramGroupId },
        config,
        { upsert: true, new: true }
      ).exec();
      return BasicUtils.okResponse(result);
    } catch (error) {
      this.logger.error('更新配置失败', error);
      return BasicUtils.errorResponseWithCodeMsg(500, '更新配置失败');
    }
  }

  /**
   * 设置Bot监听
   */
  private setupBot() {
    this.bot.on('message', async (ctx) => {
      try {
        await this.handleTelegramMessage(ctx);
      } catch (error) {
        this.logger.error('处理Telegram消息失败', error);
      }
    });
  }

  /**
   * 处理Telegram消息
   */
  private async handleTelegramMessage(ctx: any) {
    const message = ctx.message;
    const chatId = message.chat.id.toString();
    
    // 检查是否为配置的群组
    const config = await this.configService.getConfigByChatId(chatId);
    if (!config || !config.enabled) {
      return;
    }

    // 构建同步DTO
    const syncDto: SyncDto = {
      messageId: message.message_id.toString(),
      fromUser: message.from?.username || message.from?.first_name || 'Unknown',
      content: message.text || '',
      timestamp: message.date * 1000,
      chatId: chatId,
      threadId: message.message_thread_id?.toString(),
      replyTo: message.reply_to_message?.message_id?.toString(),
      isAdmin: false, // 可以根据需要实现管理员检查
    };

    // 同步到游戏
    await this.syncTelegramToGame(syncDto);
  }

  /**
   * 启动Bot监听
   */
  private async startBotListening() {
    try {
      await this.bot.launch();
      this.isListening = true;
      this.logger.log('Telegram Bot启动成功');
    } catch (error) {
      this.logger.error('启动Bot失败', error);
      this.isListening = false;
    }
  }

  /**
   * 停止Bot监听
   */
  private async stopBotListening() {
    try {
      this.bot.stop();
      this.isListening = false;
      this.logger.log('Telegram Bot已停止');
    } catch (error) {
      this.logger.error('停止Bot失败', error);
    }
  }

  /**
   * 设置队列处理器
   */
  private setupQueueProcessor() {
    // 处理Telegram到游戏的消息
    this.syncQueue.process(jobs.TELEGRAM_TO_GAME, async (job) => {
      const { message, config } = job.data;
      await this.sendToGame(message, config);
    });

    // 处理游戏到Telegram的消息
    this.syncQueue.process(jobs.GAME_TO_TELEGRAM, async (job) => {
      const { message, config } = job.data;
      await this.sendToTelegram(message, config);
    });
  }

  /**
   * 发送消息到游戏
   */
  private async sendToGame(message: any, config: any) {
    try {
      const response = await axios.post(
        `${process.env.GAME_CHAT_API_URL}/api/chat/message`,
        {
          chatId: config.gameChatChannelId,
          content: message.content,
          playerId: message.fromUser,
          isAdmin: message.isAdmin || false,
          replyTo: message.replyTo,
        },
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 10000,
        }
      );

      // 存储消息映射
      if (response.data?.messageId) {
        await this.messageService.storeMessageMapping(
          message.messageId,
          response.data.messageId,
          'telegram'
        );
      }

      this.logger.log(`消息已发送到游戏: ${message.messageId}`);
    } catch (error) {
      this.logger.error('发送消息到游戏失败', error);
      throw error;
    }
  }

  /**
   * 发送消息到Telegram
   */
  private async sendToTelegram(message: any, config: any) {
    try {
      const options: any = { parse_mode: 'HTML' };
      
      if (config.telegramThreadId) {
        options.message_thread_id = config.telegramThreadId;
      }

      if (message.replyTo) {
        options.reply_to_message_id = message.replyTo;
      }

      const result = await this.bot.telegram.sendMessage(
        config.telegramGroupId,
        `[${message.fromUser}] ${message.content}`,
        options
      );

      // 存储消息映射
      await this.messageService.storeMessageMapping(
        message.messageId,
        result.message_id.toString(),
        'game'
      );

      this.logger.log(`消息已发送到Telegram: ${message.messageId}`);
    } catch (error) {
      this.logger.error('发送消息到Telegram失败', error);
      throw error;
    }
  }
}
