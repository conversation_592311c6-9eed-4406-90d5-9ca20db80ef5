import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { jobs, queues } from '../../../constant/mq.constant';
import { Job } from 'bull';
import { GameService } from '../game.service';
import { ErrorUtils } from 'src/utils/error.utils';

@Injectable()
@Processor(queues.GAME_CHAT_SYNC_TG_QUEUE)
export class GameToTelegramHandle {
  private readonly logger: Logger = new Logger(GameToTelegramHandle.name);

  constructor(private readonly gameService: GameService) {}

  @Process(jobs.GAME_CHAT_SYNC_TG_JOB)
  async handleTask(job: Job) {
    const { message, targetMapping } = job.data;
    try {
      this.logger.log(
        `开始处理游戏消息推送任务: ${job.id}, 消息ID: ${message?.messageId}, 目标群组: ${targetMapping?.telegramGroupId || '默认'}`,
      );
      // 调用游戏服务处理消息推送，传递完整的数据对象
      await this.gameService.handleSyncToTelegram({ message, targetMapping });
      this.logger.log(`游戏消息推送任务完成: ${job.id}`);
    } catch (error) {
      const errorInfo = ErrorUtils.formatError(error, '游戏队列消息推送', {
        jobId: job.id,
        messageId: message?.messageId,
        targetGroupId: targetMapping?.telegramGroupId,
        operation: 'gameToTelegramHandle',
      });
      ErrorUtils.logError(this.logger, errorInfo, message?.messageId);

      throw error;
    }
  }
}
